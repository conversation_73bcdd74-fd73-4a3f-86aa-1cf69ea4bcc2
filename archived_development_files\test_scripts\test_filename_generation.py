#!/usr/bin/env python3
"""
测试文件命名逻辑
"""

import re
from datetime import datetime

def test_filename_generation():
    """测试文件命名逻辑"""
    
    # 模拟账号名
    account_name = "测试账号"
    
    # 模拟执行时间
    execution_time = datetime.now()
    time_str = execution_time.strftime('%Y%m%d%H%M%S')
    
    # 测试用例
    test_cases = [
        {
            'custom_title': '会说话的鹦鹉奇遇记',
            'first_sentence': '今天发生了一件有趣的事情，我在公园里遇到了一只会说话的鹦鹉。',
            'expected_source': 'custom_title'
        },
        {
            'custom_title': '',
            'first_sentence': '昨天我去了一家新开的咖啡店，那里的咖啡味道真的很棒。',
            'expected_source': 'first_sentence'
        },
        {
            'custom_title': None,
            'first_sentence': '这是一个关于友谊的故事，两个陌生人因为一本书而成为了好朋友。',
            'expected_source': 'first_sentence'
        },
        {
            'custom_title': '',
            'first_sentence': '',
            'expected_source': 'default'
        }
    ]
    
    print("文件命名逻辑测试:")
    print(f"账号名: {account_name}")
    print(f"执行时间: {time_str}")
    print()
    
    for i, case in enumerate(test_cases, 1):
        print(f"测试用例 {i}:")
        print(f"  自定义标题: {case['custom_title']}")
        print(f"  第一句话: {case['first_sentence']}")
        
        # 模拟文件命名逻辑
        title_for_filename = case['custom_title']
        if not title_for_filename or title_for_filename.strip() == '':
            # 如果没有自定义标题，使用第一句文案
            title_for_filename = case['first_sentence']
            if not title_for_filename or title_for_filename.strip() == '':
                title_for_filename = '默认文案'
        
        # 清理文件名中的特殊字符，保留中文、英文、数字、下划线和连字符
        safe_account_name = re.sub(r'[^\w\u4e00-\u9fff\-]', '_', account_name)
        safe_title_for_filename = re.sub(r'[^\w\u4e00-\u9fff\-\s]', '_', title_for_filename.strip())
        
        # 限制标题的长度，避免文件名过长
        if len(safe_title_for_filename) > 100:
            safe_title_for_filename = safe_title_for_filename[:100]
        
        # 生成文件名：账号名_执行时间_标题.扩展名
        filename = f"{safe_account_name}_{time_str}_{safe_title_for_filename}.mp4"
        
        print(f"  生成的文件名: {filename}")
        print(f"  使用的来源: {case['expected_source']}")
        print()

if __name__ == "__main__":
    test_filename_generation()
