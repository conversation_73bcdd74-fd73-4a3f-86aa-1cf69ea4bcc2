#!/usr/bin/env python3
"""
调试12秒定格问题 - 深入分析根本原因
"""

import sys
import os
import logging
from pathlib import Path
import ffmpeg
import subprocess
import time

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_manual_xfade_command():
    """手动构建xfade命令测试是否也会12秒定格"""
    
    logger.info("🔍 测试1: 手动构建xfade命令")
    
    try:
        # 创建3个测试视频
        videos = [
            ('red', '#FF0000', 5.0),
            ('green', '#00FF00', 5.0),
            ('blue', '#0000FF', 5.0)
        ]
        
        for name, color, duration in videos:
            filename = f"debug_{name}.mp4"
            logger.info(f"创建 {filename}")
            
            (
                ffmpeg
                .input(f'color={color}:size=640x480:duration={duration}:rate=30', f='lavfi')
                .output(filename, vcodec='libx264', pix_fmt='yuv420p')
                .overwrite_output()
                .run(quiet=True)
            )
        
        # 手动构建命令 - 使用最简单的方式
        cmd = [
            'ffmpeg',
            '-i', 'debug_red.mp4',
            '-i', 'debug_green.mp4', 
            '-i', 'debug_blue.mp4',
            '-filter_complex',
            '[0:v][1:v]xfade=transition=fade:duration=1:offset=4[v01];[v01][2:v]xfade=transition=fade:duration=1:offset=8',
            '-c:v', 'libx264',
            '-preset', 'ultrafast',
            '-y',
            'manual_xfade_test.mp4'
        ]
        
        logger.info("手动构建的FFmpeg命令:")
        logger.info(' '.join(cmd))
        
        # 执行命令
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        end_time = time.time()
        
        logger.info(f"命令执行时间: {end_time - start_time:.2f}秒")
        
        if result.returncode == 0:
            if Path('manual_xfade_test.mp4').exists():
                file_size = Path('manual_xfade_test.mp4').stat().st_size
                logger.info("✅ 手动xfade命令执行成功!")
                logger.info(f"文件大小: {file_size} bytes")
                logger.info("预期时长: 13秒 (5+5+5-2)")
                logger.info("关键测试: 播放到12秒后是否定格")
                return True
            else:
                logger.error("❌ 输出文件不存在")
                return False
        else:
            logger.error(f"❌ 手动命令执行失败: {result.returncode}")
            logger.error(f"stderr: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 手动测试失败: {e}")
        return False

def test_simple_concat_method():
    """测试简单的concat方法作为对比"""
    
    logger.info("\n🔍 测试2: 简单concat方法(无转场)")
    
    try:
        # 使用相同的视频文件
        cmd = [
            'ffmpeg',
            '-i', 'debug_red.mp4',
            '-i', 'debug_green.mp4',
            '-i', 'debug_blue.mp4',
            '-filter_complex',
            '[0:v][1:v][2:v]concat=n=3:v=1:a=0',
            '-c:v', 'libx264',
            '-preset', 'ultrafast',
            '-y',
            'simple_concat_test.mp4'
        ]
        
        logger.info("简单concat命令:")
        logger.info(' '.join(cmd))
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            if Path('simple_concat_test.mp4').exists():
                file_size = Path('simple_concat_test.mp4').stat().st_size
                logger.info("✅ 简单concat执行成功!")
                logger.info(f"文件大小: {file_size} bytes")
                logger.info("预期时长: 15秒")
                logger.info("测试: 这个视频应该能正常播放到结束")
                return True
            else:
                logger.error("❌ concat输出文件不存在")
                return False
        else:
            logger.error(f"❌ concat命令执行失败: {result.returncode}")
            logger.error(f"stderr: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ concat测试失败: {e}")
        return False

def test_different_durations():
    """测试不同时长的视频是否还是12秒定格"""
    
    logger.info("\n🔍 测试3: 不同时长视频测试")
    
    try:
        # 创建不同时长的视频
        videos = [
            ('short1', '#FF0000', 2.0),
            ('short2', '#00FF00', 3.0),
            ('short3', '#0000FF', 2.0)
        ]
        
        for name, color, duration in videos:
            filename = f"debug_{name}.mp4"
            logger.info(f"创建 {filename} ({duration}s)")
            
            (
                ffmpeg
                .input(f'color={color}:size=640x480:duration={duration}:rate=30', f='lavfi')
                .output(filename, vcodec='libx264', pix_fmt='yuv420p')
                .overwrite_output()
                .run(quiet=True)
            )
        
        # 构建命令 - 总时长只有7秒，看是否还会12秒定格
        cmd = [
            'ffmpeg',
            '-i', 'debug_short1.mp4',
            '-i', 'debug_short2.mp4',
            '-i', 'debug_short3.mp4',
            '-filter_complex',
            '[0:v][1:v]xfade=transition=fade:duration=0.5:offset=1.5[v01];[v01][2:v]xfade=transition=fade:duration=0.5:offset=3.5',
            '-c:v', 'libx264',
            '-preset', 'ultrafast',
            '-y',
            'short_duration_test.mp4'
        ]
        
        logger.info("短时长视频命令:")
        logger.info(' '.join(cmd))
        logger.info("预期总时长: 6秒 (2+3+2-1)")
        logger.info("关键测试: 如果还是12秒定格，说明不是时长问题")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            if Path('short_duration_test.mp4').exists():
                file_size = Path('short_duration_test.mp4').stat().st_size
                logger.info("✅ 短时长测试执行成功!")
                logger.info(f"文件大小: {file_size} bytes")
                return True
            else:
                logger.error("❌ 短时长输出文件不存在")
                return False
        else:
            logger.error(f"❌ 短时长命令执行失败: {result.returncode}")
            logger.error(f"stderr: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 短时长测试失败: {e}")
        return False

def cleanup_debug_files():
    """清理调试文件"""
    debug_files = [
        'debug_red.mp4', 'debug_green.mp4', 'debug_blue.mp4',
        'debug_short1.mp4', 'debug_short2.mp4', 'debug_short3.mp4',
        'manual_xfade_test.mp4', 'simple_concat_test.mp4', 'short_duration_test.mp4'
    ]
    
    for file_path in debug_files:
        if Path(file_path).exists():
            Path(file_path).unlink()
            logger.info(f"清理: {file_path}")

if __name__ == "__main__":
    logger.info("🚀 开始调试12秒定格问题")
    logger.info("目标: 确定是FFmpeg问题还是我的代码问题")
    
    try:
        # 测试1: 手动xfade命令
        manual_success = test_manual_xfade_command()
        
        # 测试2: 简单concat方法
        concat_success = test_simple_concat_method()
        
        # 测试3: 不同时长测试
        short_success = test_different_durations()
        
        logger.info("\n📊 调试结果分析:")
        logger.info(f"手动xfade命令: {'✅ 成功' if manual_success else '❌ 失败'}")
        logger.info(f"简单concat方法: {'✅ 成功' if concat_success else '❌ 失败'}")
        logger.info(f"短时长测试: {'✅ 成功' if short_success else '❌ 失败'}")
        
        logger.info("\n🔍 请手动播放生成的视频文件:")
        logger.info("1. manual_xfade_test.mp4 - 检查是否12秒定格")
        logger.info("2. simple_concat_test.mp4 - 检查是否能正常播放15秒")
        logger.info("3. short_duration_test.mp4 - 检查6秒视频是否正常")
        
        logger.info("\n💡 分析指导:")
        logger.info("- 如果手动xfade也12秒定格 → FFmpeg xfade滤镜问题")
        logger.info("- 如果concat正常播放 → 不是视频编码问题")
        logger.info("- 如果短视频也定格 → 不是时长相关问题")
        
    finally:
        # 不自动清理，让用户可以检查文件
        logger.info("\n注意: 调试文件未自动清理，请手动检查后删除")
        # cleanup_debug_files()
