#!/usr/bin/env python3
"""
诊断转场问题 - 深入分析10-12秒后定格的根本原因
"""

import sys
import os
import logging
from pathlib import Path
import ffmpeg
import tempfile
import time
import subprocess

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.video_generation_helpers import VideoCompositionService

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def analyze_video_info(video_path):
    """分析视频文件的详细信息"""
    try:
        probe = ffmpeg.probe(video_path)
        video_stream = next((stream for stream in probe['streams'] if stream['codec_type'] == 'video'), None)
        
        if video_stream:
            duration = float(video_stream.get('duration', 0))
            fps = eval(video_stream.get('r_frame_rate', '30/1'))
            width = video_stream.get('width', 0)
            height = video_stream.get('height', 0)
            
            logger.info(f"{video_path}:")
            logger.info(f"  时长: {duration}s")
            logger.info(f"  帧率: {fps}fps")
            logger.info(f"  分辨率: {width}x{height}")
            
            return {
                'duration': duration,
                'fps': fps,
                'width': width,
                'height': height
            }
    except Exception as e:
        logger.error(f"分析视频失败: {e}")
        return None

def create_test_videos_with_analysis():
    """创建测试视频并分析其属性"""
    
    logger.info("🎬 创建测试视频并分析属性...")
    
    # 创建不同时长的视频
    video_configs = [
        ('red', '#FF0000', 2.0),
        ('green', '#00FF00', 3.0), 
        ('blue', '#0000FF', 1.5),
        ('yellow', '#FFFF00', 2.5)
    ]
    
    test_videos = []
    actual_durations = []
    
    for name, color, duration in video_configs:
        output_path = f"diag_{name}.mp4"
        
        logger.info(f"创建{duration}s {name}视频...")
        
        # 创建视频
        (
            ffmpeg
            .input(f'color={color}:size=640x480:duration={duration}:rate=30', f='lavfi')
            .output(output_path, vcodec='libx264', pix_fmt='yuv420p', r=30)
            .overwrite_output()
            .run(quiet=True)
        )
        
        # 分析实际属性
        info = analyze_video_info(output_path)
        if info:
            actual_durations.append(info['duration'])
        else:
            actual_durations.append(duration)
        
        test_videos.append(output_path)
    
    logger.info(f"\n预期时长: {[config[2] for config in video_configs]}")
    logger.info(f"实际时长: {actual_durations}")
    
    return test_videos, actual_durations

def test_manual_xfade_command():
    """手动构建和测试xfade命令"""
    
    logger.info("\n🔧 手动构建xfade命令...")
    
    test_videos, durations = create_test_videos_with_analysis()
    
    # 手动构建FFmpeg命令
    transition_duration = 0.5
    
    logger.info(f"视频文件: {test_videos}")
    logger.info(f"实际时长: {durations}")
    logger.info(f"转场时长: {transition_duration}s")
    
    # 构建复杂的xfade命令
    cmd_parts = ['ffmpeg']
    
    # 添加输入文件
    for video in test_videos:
        cmd_parts.extend(['-i', video])
    
    # 构建滤镜图
    filter_complex = []
    
    # 统一帧率
    for i in range(len(test_videos)):
        filter_complex.append(f'[{i}:v]fps=30[v{i}]')
    
    # 第一个转场
    offset1 = durations[0] - transition_duration
    filter_complex.append(f'[v0][v1]xfade=transition=fade:duration={transition_duration}:offset={offset1}[t1]')
    
    # 第二个转场
    # 第一个转场后的时长 = durations[0] + durations[1] - transition_duration
    intermediate_duration = durations[0] + durations[1] - transition_duration
    offset2 = intermediate_duration - transition_duration
    filter_complex.append(f'[t1][v2]xfade=transition=fade:duration={transition_duration}:offset={offset2}[t2]')
    
    # 第三个转场
    intermediate_duration2 = intermediate_duration + durations[2] - transition_duration
    offset3 = intermediate_duration2 - transition_duration
    filter_complex.append(f'[t2][v3]xfade=transition=fade:duration={transition_duration}:offset={offset3}[out]')
    
    filter_string = ';'.join(filter_complex)
    
    cmd_parts.extend([
        '-filter_complex', filter_string,
        '-map', '[out]',
        '-c:v', 'libx264',
        '-preset', 'fast',
        '-y',
        'manual_xfade_test.mp4'
    ])
    
    logger.info("FFmpeg命令:")
    logger.info(' '.join(cmd_parts))
    
    logger.info(f"\n计算的offset值:")
    logger.info(f"转场1 offset: {offset1}s")
    logger.info(f"转场2 offset: {offset2}s") 
    logger.info(f"转场3 offset: {offset3}s")
    
    # 执行命令
    try:
        logger.info("执行手动xfade命令...")
        result = subprocess.run(cmd_parts, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            if Path('manual_xfade_test.mp4').exists():
                logger.info("✅ 手动xfade命令执行成功!")
                
                # 分析结果视频
                result_info = analyze_video_info('manual_xfade_test.mp4')
                if result_info:
                    expected_duration = sum(durations) - 3 * transition_duration
                    logger.info(f"预期时长: {expected_duration}s")
                    logger.info(f"实际时长: {result_info['duration']}s")
                    
                    if abs(result_info['duration'] - expected_duration) < 0.1:
                        logger.info("✅ 时长计算正确!")
                    else:
                        logger.warning("⚠️ 时长计算可能有误")
                
                return True
            else:
                logger.error("❌ 输出文件不存在")
                return False
        else:
            logger.error(f"❌ FFmpeg执行失败，返回码: {result.returncode}")
            logger.error(f"stderr: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error("❌ 命令执行超时")
        return False
    except Exception as e:
        logger.error(f"❌ 执行过程中发生错误: {e}")
        return False

def test_our_implementation():
    """测试我们的实现"""
    
    logger.info("\n🧪 测试我们的转场实现...")
    
    test_videos, durations = create_test_videos_with_analysis()
    
    try:
        # 创建视频流
        streams = []
        for video_path in test_videos:
            stream = ffmpeg.input(video_path)
            streams.append(stream)
        
        # 使用我们的实现
        final_stream = VideoCompositionService._create_video_with_transitions(
            streams, 'fade', 0.5, durations
        )
        
        # 输出
        out = ffmpeg.output(
            final_stream,
            'our_implementation_test.mp4',
            vcodec='libx264',
            preset='fast',
            pix_fmt='yuv420p'
        ).overwrite_output()
        
        # 获取命令
        cmd = ffmpeg.compile(out)
        logger.info("我们生成的FFmpeg命令:")
        logger.info(' '.join(cmd))
        
        # 执行
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            if Path('our_implementation_test.mp4').exists():
                logger.info("✅ 我们的实现执行成功!")
                
                # 分析结果
                result_info = analyze_video_info('our_implementation_test.mp4')
                if result_info:
                    expected_duration = sum(durations) - 3 * 0.5
                    logger.info(f"预期时长: {expected_duration}s")
                    logger.info(f"实际时长: {result_info['duration']}s")
                
                return True
            else:
                logger.error("❌ 输出文件不存在")
                return False
        else:
            logger.error(f"❌ 我们的实现执行失败，返回码: {result.returncode}")
            logger.error(f"stderr: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def cleanup_test_files():
    """清理测试文件"""
    test_files = [
        'diag_red.mp4', 'diag_green.mp4', 'diag_blue.mp4', 'diag_yellow.mp4',
        'manual_xfade_test.mp4', 'our_implementation_test.mp4'
    ]
    
    for file_path in test_files:
        if Path(file_path).exists():
            Path(file_path).unlink()
            logger.info(f"清理: {file_path}")

if __name__ == "__main__":
    logger.info("🚀 开始诊断转场问题")
    logger.info("目标: 找出10-12秒后定格的根本原因")
    
    try:
        # 1. 测试手动构建的xfade命令
        manual_success = test_manual_xfade_command()
        
        # 2. 测试我们的实现
        our_success = test_our_implementation()
        
        logger.info("\n📊 诊断结果:")
        logger.info(f"手动xfade命令: {'✅ 成功' if manual_success else '❌ 失败'}")
        logger.info(f"我们的实现: {'✅ 成功' if our_success else '❌ 失败'}")
        
        if manual_success and not our_success:
            logger.info("🔍 问题在我们的实现中，需要对比两个命令的差异")
        elif not manual_success:
            logger.info("🔍 问题可能在xfade本身的使用方式上")
        else:
            logger.info("🎉 两个测试都成功，问题可能在其他地方")
            
    finally:
        cleanup_test_files()
