#!/usr/bin/env python3
"""
测试原始视频生成流程 - 验证封面修复是否在实际流程中生效
"""

import sys
import os
import logging
from pathlib import Path
import ffmpeg
import tempfile

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.video_generation_helpers import VideoCompositionService

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_original_flow():
    """测试原始视频生成流程中的封面处理"""
    
    # 测试文件路径
    video_path = "uploads/videos/RedditTour_07030014_视频1.mp4"
    cover_path = "uploads/covers/1_02c58438-97a3-435f-8ed0-5b669bc167ed_cover.png"
    output_path = "debug_original_flow.mp4"
    
    # 检查文件是否存在
    if not Path(video_path).exists():
        logger.error(f"测试视频文件不存在: {video_path}")
        return False
        
    if not Path(cover_path).exists():
        logger.error(f"测试封面文件不存在: {cover_path}")
        return False
    
    logger.info("🔄 开始测试原始视频生成流程...")
    logger.info(f"视频: {video_path}")
    logger.info(f"封面: {cover_path}")
    logger.info(f"输出: {output_path}")
    
    try:
        # 获取视频信息
        probe = ffmpeg.probe(video_path)
        video_info = next(s for s in probe['streams'] if s['codec_type'] == 'video')
        width = int(video_info['width'])
        height = int(video_info['height'])
        duration = float(video_info['duration'])
        
        logger.info(f"视频信息: {width}x{height}, 时长: {duration}s")
        
        # 模拟原始流程的参数
        cover_width = int(width * 0.8)
        corner_radius = int(cover_width * 0.06)
        cover_duration = 3.0
        
        logger.info(f"封面配置: 宽度={cover_width}px, 圆角={corner_radius}px, 时长={cover_duration}s")
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # 步骤1: 创建基础缩放后的封面（模拟原始流程）
            logger.info("步骤1: 创建基础缩放封面")
            cover_scaled = (
                ffmpeg
                .input(filename=cover_path)
                .filter('scale', cover_width, -1)  # -1 表示高度自动计算以保持纵横比
            )
            
            # 步骤2: 创建圆角封面图像（使用修复后的函数）
            logger.info("步骤2: 创建圆角封面图像")
            rounded_cover_path = temp_path / "rounded_cover_test.png"
            
            success = VideoCompositionService._create_rounded_cover_image(
                cover_path, cover_width, corner_radius, str(rounded_cover_path)
            )
            
            if not success or not rounded_cover_path.exists():
                logger.error("❌ 圆角封面创建失败，回退到方角封面")
                cover_overlay = cover_scaled
            else:
                logger.info("✅ 圆角封面创建成功，使用圆角封面")
                cover_overlay = ffmpeg.input(filename=str(rounded_cover_path))
            
            # 步骤3: 应用封面叠加效果（模拟原始流程）
            logger.info("步骤3: 应用封面叠加效果")
            
            # 创建视频流
            video_stream = ffmpeg.input(video_path)
            
            # 模拟封面设置
            cover_settings = {
                'position': 'center',
                'animation': 'fade_in_out',
                'animation_duration': 0.5
            }
            
            # 使用原始的 _apply_cover_overlay 方法
            final_video = VideoCompositionService._apply_cover_overlay(
                video_stream, cover_overlay, cover_duration, cover_settings
            )
            
            # 步骤4: 输出最终视频
            logger.info("步骤4: 输出最终视频")
            
            out = ffmpeg.output(
                final_video,
                output_path,
                vcodec='libx264',
                preset='fast',
                pix_fmt='yuv420p',
                t=5  # 只生成5秒测试视频
            ).overwrite_output()
            
            logger.info("开始执行FFmpeg命令...")
            
            # 显示FFmpeg命令
            cmd = ffmpeg.compile(out)
            logger.info(f"FFmpeg命令: {' '.join(cmd)}")
            
            # 执行
            ffmpeg.run(out, quiet=False)
            
            # 检查结果
            if Path(output_path).exists():
                file_size = Path(output_path).stat().st_size
                logger.info(f"✅ 测试成功! 输出文件: {output_path}, 大小: {file_size} bytes")
                
                # 验证输出视频信息
                try:
                    output_probe = ffmpeg.probe(output_path)
                    output_info = next(s for s in output_probe['streams'] if s['codec_type'] == 'video')
                    output_width = int(output_info['width'])
                    output_height = int(output_info['height'])
                    logger.info(f"输出视频尺寸: {output_width}x{output_height}")
                    
                    if output_width == width and output_height == height:
                        logger.info("✅ 视频尺寸验证通过")
                    else:
                        logger.warning(f"⚠️ 视频尺寸不匹配: 期望{width}x{height}, 实际{output_width}x{output_height}")
                        
                except Exception as e:
                    logger.warning(f"无法验证输出视频信息: {e}")
                
                return True
            else:
                logger.error("❌ 测试失败: 输出文件不存在")
                return False
                
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        return False

def compare_with_simple_test():
    """对比简单测试和原始流程的结果"""
    
    logger.info("\n🔄 开始对比测试...")
    
    # 文件路径
    original_flow_video = "debug_original_flow.mp4"
    simple_test_video = "debug_cover_fix.mp4"
    
    if not Path(original_flow_video).exists():
        logger.error(f"原始流程视频不存在: {original_flow_video}")
        return False
        
    if not Path(simple_test_video).exists():
        logger.error(f"简单测试视频不存在: {simple_test_video}")
        return False
    
    try:
        # 获取两个视频的信息
        original_probe = ffmpeg.probe(original_flow_video)
        original_info = next(s for s in original_probe['streams'] if s['codec_type'] == 'video')
        
        simple_probe = ffmpeg.probe(simple_test_video)
        simple_info = next(s for s in simple_probe['streams'] if s['codec_type'] == 'video')
        
        logger.info(f"原始流程视频: {original_info['width']}x{original_info['height']}")
        logger.info(f"简单测试视频: {simple_info['width']}x{simple_info['height']}")
        
        if (original_info['width'] == simple_info['width'] and 
            original_info['height'] == simple_info['height']):
            logger.info("✅ 两个视频尺寸一致")
        else:
            logger.warning("⚠️ 两个视频尺寸不一致")
        
        # 获取文件大小
        original_size = Path(original_flow_video).stat().st_size
        simple_size = Path(simple_test_video).stat().st_size
        
        logger.info(f"原始流程文件大小: {original_size} bytes")
        logger.info(f"简单测试文件大小: {simple_size} bytes")
        
        return True
        
    except Exception as e:
        logger.error(f"对比测试失败: {e}")
        return False

if __name__ == "__main__":
    logger.info("🚀 开始原始流程测试")
    
    # 测试1: 原始视频生成流程
    success1 = test_original_flow()
    
    # 测试2: 对比结果
    success2 = compare_with_simple_test()
    
    if success1 and success2:
        logger.info("\n🎉 原始流程测试通过!")
        logger.info("✅ 封面修复在实际视频生成流程中正常工作")
    else:
        logger.error("\n❌ 原始流程测试失败")
        sys.exit(1)
