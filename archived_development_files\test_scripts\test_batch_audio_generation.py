#!/usr/bin/env python3
"""
测试新的批量语音生成流程
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from src.core.database import get_session_maker
from src.models.video_generation import VideoGenerationJob, VideoGenerationTask, TaskStatus
from src.models.accounts import Account
from src.schemas.video_generation import CreateBatchVideoGenerationJobRequest, VideoGenerationConfig
from src.services.video_generation_service import VideoGenerationService
from loguru import logger

async def test_batch_audio_generation():
    """测试批量语音生成功能"""
    
    print("🎵 测试批量语音生成流程")
    print("=" * 50)
    
    # 1. 获取数据库会话
    session_maker = get_session_maker()
    db = session_maker()
    
    try:
        # 2. 检查账号
        print("\n📋 检查可用账号...")
        accounts = db.query(Account).filter(Account.status == "unused").limit(2).all()
        
        if len(accounts) < 1:
            print("❌ 需要至少1个可用账号")
            return False
        
        account_ids = [account.id for account in accounts]
        print(f"✅ 找到 {len(accounts)} 个可用账号")
        for account in accounts:
            print(f"   - {account.name} ({account.id})")
        
        # 3. 准备测试文案
        test_stories = [
            "这是第一个测试文案，用于验证批量语音生成功能。",
            "这是第二个测试文案，我们要确保所有语音都先生成完成。",
            "这是第三个测试文案，然后再进行视频合成阶段。"
        ]
        
        print(f"\n📝 准备了 {len(test_stories)} 个测试文案")
        
        # 4. 创建测试配置
        config = VideoGenerationConfig(
            video_material_group="default",
            material_selection="random",
            prompt_group="default", 
            prompt_id="default",
            voice_settings={
                "voice": "default",
                "speed": 1.0
            },
            background_music_group="default",
            music_selection="random",
            cover_template_id="default",
            subtitle_config={
                "font": "Arial",
                "size": 24,
                "color": "#FFFFFF"
            },
            video_settings={
                "resolution": "1080x1920",
                "fps": 30,
                "format": "mp4"
            }
        )
        
        # 5. 创建批量生成请求
        request = CreateBatchVideoGenerationJobRequest(
            name="批量语音生成测试",
            description="测试新的分阶段批量处理流程",
            config=config,
            account_ids=account_ids,
            stories=test_stories
        )
        
        print("✅ 批量生成配置创建完成")
        
        # 6. 创建批量作业
        print("\n🎬 创建批量视频生成作业...")
        
        service = VideoGenerationService(session_maker)
        job = await service.create_batch_job(request)
        
        print(f"✅ 批量作业创建成功!")
        print(f"   作业ID: {job.id}")
        print(f"   作业名称: {job.name}")
        print(f"   总任务数: {job.total_tasks}")
        print(f"   状态: {job.status}")
        
        # 7. 验证任务状态
        print("\n🔍 验证任务初始状态...")
        
        tasks = db.query(VideoGenerationTask).filter(VideoGenerationTask.job_id == job.id).all()
        
        print(f"✅ 创建了 {len(tasks)} 个任务")
        for i, task in enumerate(tasks):
            print(f"   任务 {i+1}: {task.task_name} - 状态: {task.status}")
            if task.status != TaskStatus.AUDIO_PENDING:
                print(f"   ⚠️  期望状态: {TaskStatus.AUDIO_PENDING}, 实际状态: {task.status}")
        
        # 8. 启动作业
        print(f"\n🚀 启动作业 {job.id}...")
        await service.start_job(job.id)
        
        # 9. 监控语音生成阶段
        print("\n🎵 监控语音生成阶段...")
        audio_phase_completed = False
        max_wait_time = 300  # 最多等待5分钟
        wait_time = 0
        
        while not audio_phase_completed and wait_time < max_wait_time:
            await asyncio.sleep(5)
            wait_time += 5
            
            # 刷新任务状态
            db.commit()  # 确保获取最新状态
            tasks = db.query(VideoGenerationTask).filter(VideoGenerationTask.job_id == job.id).all()
            
            # 统计各状态的任务数
            status_counts = {}
            for task in tasks:
                status = task.status
                status_counts[status] = status_counts.get(status, 0) + 1
            
            print(f"⏱️  等待时间: {wait_time}s - 状态统计: {status_counts}")
            
            # 检查是否所有任务都完成了语音生成
            audio_pending_count = status_counts.get(TaskStatus.AUDIO_PENDING, 0)
            audio_running_count = len([t for t in tasks if t.status == TaskStatus.RUNNING and '语音' in (t.current_step or '')])
            
            if audio_pending_count == 0 and audio_running_count == 0:
                audio_completed_count = status_counts.get(TaskStatus.AUDIO_COMPLETED, 0)
                video_pending_count = status_counts.get(TaskStatus.VIDEO_PENDING, 0)
                
                if audio_completed_count > 0 or video_pending_count > 0:
                    audio_phase_completed = True
                    print("🎉 语音生成阶段完成！")
                    break
        
        if not audio_phase_completed:
            print("⚠️  语音生成阶段未在预期时间内完成")
            return False
        
        # 10. 监控视频合成阶段
        print("\n🎬 监控视频合成阶段...")
        video_phase_completed = False
        max_wait_time = 600  # 最多等待10分钟
        wait_time = 0
        
        while not video_phase_completed and wait_time < max_wait_time:
            await asyncio.sleep(10)
            wait_time += 10
            
            # 刷新任务状态
            db.commit()
            tasks = db.query(VideoGenerationTask).filter(VideoGenerationTask.job_id == job.id).all()
            
            # 统计各状态的任务数
            status_counts = {}
            for task in tasks:
                status = task.status
                status_counts[status] = status_counts.get(status, 0) + 1
            
            print(f"⏱️  等待时间: {wait_time}s - 状态统计: {status_counts}")
            
            # 检查是否所有任务都完成
            completed_count = status_counts.get(TaskStatus.COMPLETED, 0)
            failed_count = status_counts.get(TaskStatus.FAILED, 0)
            
            if completed_count + failed_count == len(tasks):
                video_phase_completed = True
                print(f"🎉 视频合成阶段完成！完成: {completed_count}, 失败: {failed_count}")
                break
        
        if not video_phase_completed:
            print("⚠️  视频合成阶段未在预期时间内完成")
            return False
        
        print("\n✅ 批量语音生成流程测试完成！")
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    success = asyncio.run(test_batch_audio_generation())
    if success:
        print("\n🎉 测试成功！")
        sys.exit(0)
    else:
        print("\n❌ 测试失败！")
        sys.exit(1)
