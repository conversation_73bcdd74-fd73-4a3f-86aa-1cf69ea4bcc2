#!/usr/bin/env python3
"""
最基础的fade测试 - 验证fade滤镜是否真的工作
"""

import subprocess
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_basic_fade():
    """最基础的fade测试"""
    
    logger.info("🔍 最基础的fade测试 - 验证fade滤镜是否工作")
    
    # 首先创建一个纯色测试视频
    logger.info("步骤1: 创建纯红色测试视频")
    cmd_create = [
        'ffmpeg', '-y',
        '-f', 'lavfi', '-i', 'color=red:size=640x480:duration=10',
        '-c:v', 'libx264',
        '-pix_fmt', 'yuv420p',
        'red_test.mp4'
    ]
    
    result = subprocess.run(cmd_create, capture_output=True, text=True)
    if result.returncode != 0:
        logger.error(f"创建红色视频失败: {result.stderr}")
        return False
    
    logger.info("✅ 红色测试视频创建成功")
    
    # 测试1: 对红色视频应用fade in
    logger.info("步骤2: 对红色视频应用fade in")
    cmd_fade_in = [
        'ffmpeg', '-y', '-v', 'verbose',
        '-i', 'red_test.mp4',
        '-vf', 'fade=in:0:150',  # 5秒淡入 (150帧 @ 30fps)
        '-c:v', 'libx264',
        'red_fade_in.mp4'
    ]
    
    logger.info(f"执行命令: {' '.join(cmd_fade_in)}")
    result1 = subprocess.run(cmd_fade_in, capture_output=True, text=True)
    
    if result1.returncode == 0:
        logger.info("✅ red_fade_in.mp4 生成成功")
        logger.info("   预期效果: 前5秒红色从黑色慢慢变为纯红色")
    else:
        logger.error(f"❌ fade in失败: {result1.stderr}")
        return False
    
    # 测试2: 对红色视频应用fade out
    logger.info("步骤3: 对红色视频应用fade out")
    cmd_fade_out = [
        'ffmpeg', '-y', '-v', 'verbose',
        '-i', 'red_test.mp4',
        '-vf', 'fade=out:150:150',  # 从第5秒开始淡出5秒
        '-c:v', 'libx264',
        'red_fade_out.mp4'
    ]
    
    logger.info(f"执行命令: {' '.join(cmd_fade_out)}")
    result2 = subprocess.run(cmd_fade_out, capture_output=True, text=True)
    
    if result2.returncode == 0:
        logger.info("✅ red_fade_out.mp4 生成成功")
        logger.info("   预期效果: 5-10秒红色从纯红色慢慢变为黑色")
    else:
        logger.error(f"❌ fade out失败: {result2.stderr}")
        return False
    
    # 测试3: 使用时间参数的fade
    logger.info("步骤4: 使用时间参数的fade")
    cmd_fade_time = [
        'ffmpeg', '-y', '-v', 'verbose',
        '-i', 'red_test.mp4',
        '-vf', 'fade=t=in:st=0:d=3,fade=t=out:st=7:d=3',
        '-c:v', 'libx264',
        'red_fade_time.mp4'
    ]
    
    logger.info(f"执行命令: {' '.join(cmd_fade_time)}")
    result3 = subprocess.run(cmd_fade_time, capture_output=True, text=True)
    
    if result3.returncode == 0:
        logger.info("✅ red_fade_time.mp4 生成成功")
        logger.info("   预期效果: 0-3秒淡入，7-10秒淡出")
    else:
        logger.error(f"❌ 时间fade失败: {result3.stderr}")
        return False
    
    # 测试4: 检查FFmpeg版本和fade滤镜支持
    logger.info("步骤5: 检查FFmpeg fade滤镜支持")
    cmd_check = ['ffmpeg', '-filters']
    result_check = subprocess.run(cmd_check, capture_output=True, text=True)
    
    if 'fade' in result_check.stdout:
        logger.info("✅ FFmpeg支持fade滤镜")
    else:
        logger.error("❌ FFmpeg不支持fade滤镜!")
        return False
    
    # 检查生成的文件
    logger.info("\n=== 文件检查 ===")
    test_files = ['red_test.mp4', 'red_fade_in.mp4', 'red_fade_out.mp4', 'red_fade_time.mp4']
    
    for filename in test_files:
        if Path(filename).exists():
            file_size = Path(filename).stat().st_size
            logger.info(f"✅ {filename}: {file_size} bytes")
        else:
            logger.error(f"❌ {filename}: 不存在")
    
    logger.info("\n🎬 验证方法:")
    logger.info("播放这些文件，如果fade滤镜工作正常，你应该看到:")
    logger.info("- red_fade_in.mp4: 红色从黑色慢慢出现")
    logger.info("- red_fade_out.mp4: 红色慢慢变为黑色")
    logger.info("- red_fade_time.mp4: 开头淡入，结尾淡出")
    
    logger.info("\n如果这些纯色视频都没有fade效果，")
    logger.info("那么问题在于FFmpeg的fade滤镜本身！")
    
    return True

def test_cover_fade_step_by_step():
    """分步测试封面fade"""
    
    logger.info("\n🔧 分步测试封面fade...")
    
    cover_path = "uploads/covers/1_02c58438-97a3-435f-8ed0-5b669bc167ed_cover.png"
    video_path = "uploads/videos/RedditTour_07030014_视频1.mp4"
    
    if not Path(cover_path).exists() or not Path(video_path).exists():
        logger.error("测试文件不存在")
        return False
    
    # 步骤1: 只对封面应用fade，不overlay
    logger.info("步骤1: 只对封面应用fade")
    cmd1 = [
        'ffmpeg', '-y',
        '-loop', '1', '-i', cover_path,
        '-vf', 'scale=400:300,fade=in:0:90,fade=out:210:90',  # 3秒淡入，7-10秒淡出
        '-t', '10',
        '-c:v', 'libx264',
        '-pix_fmt', 'yuv420p',
        'cover_fade_only.mp4'
    ]
    
    logger.info(f"命令: {' '.join(cmd1)}")
    result1 = subprocess.run(cmd1, capture_output=True, text=True)
    
    if result1.returncode == 0:
        logger.info("✅ cover_fade_only.mp4 生成成功")
        logger.info("   预期: 封面0-3秒淡入，7-10秒淡出")
    else:
        logger.error(f"❌ 失败: {result1.stderr}")
        return False
    
    # 步骤2: 将fade后的封面overlay到视频
    logger.info("步骤2: 将fade后的封面overlay到视频")
    cmd2 = [
        'ffmpeg', '-y',
        '-i', video_path,
        '-i', 'cover_fade_only.mp4',
        '-filter_complex', '[0:v][1:v]overlay=50:50',
        '-t', '10',
        '-c:v', 'libx264',
        'video_with_faded_cover.mp4'
    ]
    
    logger.info(f"命令: {' '.join(cmd2)}")
    result2 = subprocess.run(cmd2, capture_output=True, text=True)
    
    if result2.returncode == 0:
        logger.info("✅ video_with_faded_cover.mp4 生成成功")
        logger.info("   预期: 视频上有fade效果的封面")
    else:
        logger.error(f"❌ 失败: {result2.stderr}")
        return False
    
    return True

if __name__ == "__main__":
    logger.info("🚀 开始最基础的fade测试")
    
    # 测试1: 基础fade滤镜
    success1 = test_basic_fade()
    
    # 测试2: 封面fade分步测试
    success2 = test_cover_fade_step_by_step()
    
    if success1 and success2:
        logger.info("\n🎉 基础测试完成!")
        logger.info("生成的文件:")
        logger.info("- red_fade_in.mp4 (纯红色淡入)")
        logger.info("- red_fade_out.mp4 (纯红色淡出)")
        logger.info("- red_fade_time.mp4 (纯红色淡入+淡出)")
        logger.info("- cover_fade_only.mp4 (只有封面的fade)")
        logger.info("- video_with_faded_cover.mp4 (视频+fade封面)")
        
        logger.info("\n如果纯红色视频都没有fade效果，")
        logger.info("那么问题在于FFmpeg fade滤镜本身！")
    else:
        logger.error("\n❌ 基础测试失败")
