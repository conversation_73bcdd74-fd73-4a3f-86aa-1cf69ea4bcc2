#!/usr/bin/env python3
"""
测试新的批量处理流程（不启动完整服务）
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

async def test_batch_flow():
    """测试批量处理流程的逻辑"""
    
    print("🎵 测试批量处理流程逻辑")
    print("=" * 50)
    
    try:
        # 1. 测试状态导入
        from src.models.video_generation import TaskStatus, VideoGenerationJob, VideoGenerationTask
        from src.core.database import get_session_maker
        
        print("✅ 成功导入所需模块")
        
        # 2. 测试新状态
        print(f"\n📋 新增状态:")
        print(f"  AUDIO_PENDING: '{TaskStatus.AUDIO_PENDING}'")
        print(f"  AUDIO_COMPLETED: '{TaskStatus.AUDIO_COMPLETED}'")
        print(f"  VIDEO_PENDING: '{TaskStatus.VIDEO_PENDING}'")
        
        # 3. 测试数据库连接
        session_maker = get_session_maker()
        db = session_maker()
        
        print("✅ 数据库连接成功")
        
        # 4. 查看现有作业
        jobs = db.query(VideoGenerationJob).order_by(VideoGenerationJob.created_at.desc()).limit(3).all()
        print(f"\n📊 最近的 {len(jobs)} 个作业:")
        
        for job in jobs:
            tasks = db.query(VideoGenerationTask).filter(VideoGenerationTask.job_id == job.id).all()
            status_counts = {}
            for task in tasks:
                status = task.status
                status_counts[status] = status_counts.get(status, 0) + 1
            
            print(f"  作业: {job.name} ({job.id[:8]}...)")
            print(f"    状态: {job.status}, 任务数: {len(tasks)}")
            print(f"    任务状态分布: {status_counts}")
        
        # 5. 测试状态转换逻辑
        print(f"\n🔄 测试状态转换逻辑:")
        
        # 模拟语音生成阶段完成检查
        test_statuses = [
            TaskStatus.AUDIO_PENDING,
            TaskStatus.AUDIO_COMPLETED,
            TaskStatus.VIDEO_PENDING,
            TaskStatus.RUNNING,
            TaskStatus.COMPLETED
        ]
        
        for status in test_statuses:
            print(f"  状态 '{status}' 是否为未完成状态: {status in [TaskStatus.PENDING, TaskStatus.AUDIO_PENDING, TaskStatus.AUDIO_COMPLETED, TaskStatus.VIDEO_PENDING, TaskStatus.RUNNING]}")
        
        print("\n✅ 批量处理流程逻辑测试成功！")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_batch_flow())
    if success:
        print("\n🎉 测试成功！")
    else:
        print("\n❌ 测试失败！")
