"""
设置服务 - 提供设置相关的服务函数
"""

from sqlalchemy.orm import Session
from src.models.settings import Settings
from src.schemas.settings import LLMConfig
from typing import Optional

def get_current_llm_config(db: Session) -> LLMConfig:
    """获取当前的LLM配置，始终返回一个有效的LLMConfig实例"""
    try:
        settings = db.query(Settings).first()
        if not settings:
            # 如果数据库中没有设置，创建一个临时的默认实例并获取其格式化数据
            settings = Settings()
        
        frontend_data = settings.to_frontend_format()
        llm_data = frontend_data["llm"]
        
        return LLMConfig(
            provider=llm_data["provider"],
            apiKey=llm_data["apiKey"],
            endpoint=llm_data["endpoint"],
            model=llm_data["model"],
            temperature=llm_data["temperature"],
            maxTokens=llm_data["maxTokens"],
            systemPrompt=llm_data["systemPrompt"]
        )
    except Exception as e:
        print(f"获取LLM配置失败: {e}")
        # 即使出现异常，也返回一个安全的默认值
        return LLMConfig()

def get_current_tts_config(db: Session) -> dict:
    """获取当前的TTS配置，始终返回一个有效的字典"""
    try:
        settings = db.query(Settings).first()
        if not settings:
            # 如果数据库���没有设置，创建一个临时的默认实例并获取其格式化数据
            return Settings().to_frontend_format()["tts"]
        
        frontend_data = settings.to_frontend_format()
        return frontend_data["tts"]
    except Exception as e:
        print(f"获取TTS配置失败: {e}")
        # 即使出现异常，也返回一个安全的默认值
        return Settings().to_frontend_format()["tts"]
