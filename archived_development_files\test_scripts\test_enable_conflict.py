#!/usr/bin/env python3
"""
测试overlay的enable参数是否与fade冲突
"""

import subprocess
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_enable_conflict():
    """测试enable参数与fade的冲突"""
    
    video_path = "uploads/videos/RedditTour_07030014_视频1.mp4"
    cover_path = "uploads/covers/1_02c58438-97a3-435f-8ed0-5b669bc167ed_cover.png"
    
    if not Path(video_path).exists() or not Path(cover_path).exists():
        logger.error("测试文件不存在")
        return
    
    logger.info("🔍 测试overlay enable参数与fade的冲突...")
    
    # 测试1: fade + enable (可能有冲突)
    logger.info("测试1: fade + enable参数")
    cmd1 = [
        'ffmpeg', '-y',
        '-i', video_path,
        '-i', cover_path,
        '-filter_complex', 
        '[1:v]scale=400:300,fade=in:0:60[faded];[0:v][faded]overlay=50:50:enable=between(t,0,5)',
        '-t', '6',
        '-c:v', 'libx264',
        '-preset', 'ultrafast',
        'fade_with_enable.mp4'
    ]
    
    logger.info(f"命令: {' '.join(cmd1)}")
    result1 = subprocess.run(cmd1, capture_output=True, text=True)
    
    if result1.returncode == 0:
        logger.info("✅ fade_with_enable.mp4 生成成功")
    else:
        logger.error(f"❌ 失败: {result1.stderr}")
    
    # 测试2: fade 但不用enable (应该正常)
    logger.info("测试2: fade 不用enable参数")
    cmd2 = [
        'ffmpeg', '-y',
        '-i', video_path,
        '-i', cover_path,
        '-filter_complex', 
        '[1:v]scale=400:300,fade=in:0:60[faded];[0:v][faded]overlay=50:50',
        '-t', '6',
        '-c:v', 'libx264',
        '-preset', 'ultrafast',
        'fade_no_enable.mp4'
    ]
    
    logger.info(f"命令: {' '.join(cmd2)}")
    result2 = subprocess.run(cmd2, capture_output=True, text=True)
    
    if result2.returncode == 0:
        logger.info("✅ fade_no_enable.mp4 生成成功")
    else:
        logger.error(f"❌ 失败: {result2.stderr}")
    
    # 测试3: 不用fade，只用enable
    logger.info("测试3: 只用enable参数，不用fade")
    cmd3 = [
        'ffmpeg', '-y',
        '-i', video_path,
        '-i', cover_path,
        '-filter_complex', 
        '[1:v]scale=400:300[scaled];[0:v][scaled]overlay=50:50:enable=between(t,1,4)',
        '-t', '6',
        '-c:v', 'libx264',
        '-preset', 'ultrafast',
        'enable_no_fade.mp4'
    ]
    
    logger.info(f"命令: {' '.join(cmd3)}")
    result3 = subprocess.run(cmd3, capture_output=True, text=True)
    
    if result3.returncode == 0:
        logger.info("✅ enable_no_fade.mp4 生成成功")
    else:
        logger.error(f"❌ 失败: {result3.stderr}")
    
    # 测试4: 用loop让封面持续显示，然后fade
    logger.info("测试4: 用loop + fade")
    cmd4 = [
        'ffmpeg', '-y',
        '-i', video_path,
        '-loop', '1', '-i', cover_path,
        '-filter_complex', 
        '[1:v]scale=400:300,fade=in:0:60,fade=out:120:60[faded];[0:v][faded]overlay=50:50',
        '-t', '6',
        '-c:v', 'libx264',
        '-preset', 'ultrafast',
        'loop_fade.mp4'
    ]
    
    logger.info(f"命令: {' '.join(cmd4)}")
    result4 = subprocess.run(cmd4, capture_output=True, text=True)
    
    if result4.returncode == 0:
        logger.info("✅ loop_fade.mp4 生成成功")
    else:
        logger.error(f"❌ 失败: {result4.stderr}")
    
    logger.info("\n🎬 测试完成！请播放这些文件对比效果:")
    logger.info("- fade_with_enable.mp4: fade + enable (可能有冲突)")
    logger.info("- fade_no_enable.mp4: 只有fade，没有enable")
    logger.info("- enable_no_fade.mp4: 只有enable，没有fade")
    logger.info("- loop_fade.mp4: 使用loop + fade")
    
    logger.info("\n如果fade_no_enable.mp4有fade效果，而fade_with_enable.mp4没有，")
    logger.info("那么问题就是enable参数与fade冲突！")

if __name__ == "__main__":
    test_enable_conflict()
