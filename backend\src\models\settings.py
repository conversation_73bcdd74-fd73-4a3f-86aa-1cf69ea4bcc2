"""
设置相关数据模型
"""

from sqlalchemy import Column, String, Text, Boolean, Integer, DECIMAL
from . import BaseModel

class Settings(BaseModel):
    """用户设置表 - 对应前端settingsStore"""
    __tablename__ = "settings"
    
    user_id = Column(String(36), nullable=True, comment="用户ID")
      # TTS配置 - 对应前端TTSConfig
    tts_provider = Column(String(50), nullable=False, default="openai", comment="TTS提供商")
    tts_api_key = Column(Text, comment="TTS API密钥")
    tts_endpoint = Column(String(500), comment="TTS API端点")
    tts_voice = Column(String(100), nullable=False, default="alloy", comment="TTS语音")
    tts_model = Column(String(100), comment="TTS模型")
    tts_speed = Column(DECIMAL(3, 2), nullable=False, default=1.0, comment="TTS语速")
    tts_pitch = Column(DECIMAL(3, 2), nullable=False, default=1.0, comment="TTS音调")
    tts_volume = Column(DECIMAL(3, 2), nullable=False, default=1.0, comment="TTS音量")

    # F5-TTS特有配置
    f5_tts_endpoint = Column(String(500), comment="F5-TTS服务端点")

      # LLM配置 - 对应前端LLMConfig
    llm_provider = Column(String(50), nullable=False, default="openai", comment="LLM提供商")
    llm_api_key = Column(Text, comment="LLM API密钥")
    llm_base_url = Column(String(500), comment="LLM API端点")
    llm_model = Column(String(100), nullable=False, default="gpt-3.5-turbo", comment="LLM模型")
    llm_temperature = Column(DECIMAL(3, 2), nullable=False, default=0.7, comment="LLM温度")
    llm_max_tokens = Column(Integer, nullable=False, default=2000, comment="LLM最大Token数")
    llm_system_prompt = Column(Text, comment="LLM系统提示词")    
    # 通用设置 - 对应前端GeneralSettings
    theme = Column(String(20), nullable=False, default="light", comment="主题")
    language = Column(String(10), nullable=False, default="zh-CN", comment="语言")
    auto_save = Column(Boolean, nullable=False, default=True, comment="自动保存")
    show_tips = Column(Boolean, nullable=False, default=True, comment="显示提示")
    output_directory = Column(String(500), comment="输出目录")
    
    def to_frontend_format(self):
        """转换为前端期望的格式，确保所有键都存在"""
        return {
            "tts": {
                "provider": getattr(self, 'tts_provider', 'coze') or 'coze',
                "apiKey": getattr(self, 'tts_api_key', '') or '',
                "endpoint": getattr(self, 'tts_endpoint', '') or "https://api.coze.cn/v1/workflow/run",
                "voice": getattr(self, 'tts_voice', 'zh_male_wennuanahu_moon_bigtts') or 'zh_male_wennuanahu_moon_bigtts',
                "model": getattr(self, 'tts_model', '') or '',
                "speed": float(getattr(self, 'tts_speed', 1.2) or 1.2),
                "pitch": float(getattr(self, 'tts_pitch', 1.0) or 1.0),
                "volume": float(getattr(self, 'tts_volume', 1.0) or 1.0),
                # F5-TTS特有配置
                "f5TtsEndpoint": getattr(self, 'f5_tts_endpoint', '') or ''
            },
            "llm": {
                "provider": getattr(self, 'llm_provider', 'yunwu') or 'yunwu',
                "apiKey": getattr(self, 'llm_api_key', '') or "",
                "endpoint": getattr(self, 'llm_base_url', '') or "https://yunwu.ai",
                "model": getattr(self, 'llm_model', 'gpt-3.5-turbo'),
                "temperature": float(getattr(self, 'llm_temperature', 0.7) or 0.7),
                "maxTokens": int(getattr(self, 'llm_max_tokens', 2000) or 2000),
                "systemPrompt": getattr(self, 'llm_system_prompt', '') or ""
            },
            "general": {
                "theme": getattr(self, 'theme', 'light') or 'light',
                "language": getattr(self, 'language', 'zh-CN') or 'zh-CN',
                "autoSave": getattr(self, 'auto_save', True),
                "showTips": getattr(self, 'show_tips', True),
                "outputDirectory": getattr(self, 'output_directory', '') or ""
            }
        }
