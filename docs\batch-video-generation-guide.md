# 批量视频生成功能使用指南

## 功能概述

批量视频生成功能允许用户通过上传Excel文件，基于预设的文案列表批量生成视频。系统会自动将文案按顺序轮流分配给选中的账号进行视频生成。

## 主要特性

- **Excel文案上传**: 支持.xlsx和.xls格式的Excel文件
- **智能文案解析**: 自动提取Excel第一列的文案内容
- **账号轮流分配**: 按文案顺序轮流分配给选中的账号
- **配置复用**: 复用现有的视频生成配置（语音、素材、音乐等）
- **任务兼容**: 与现有任务管理系统完全兼容

## 使用步骤

### 1. 准备Excel文件

创建一个Excel文件，将文案内容放在第一列：

```
A列（文案内容）
今天发生了一件有趣的事情...
昨天我遇到了一个奇怪的人...
这是一个关于友谊的故事...
```

### 2. 访问批量生成页面

- 在主页点击"批量生成视频"按钮
- 或直接访问 `/batch-generate` 页面

### 3. 上传Excel文件

1. 点击上传区域选择Excel文件
2. 系统会自动解析文案内容
3. 在预览区域查看解析结果

### 4. 选择账号

- 选择一个或多个账号用于视频生成
- 文案将按顺序轮流分配给选中的账号

### 5. 配置生成参数

配置以下参数：
- **语音设置**: 选择TTS音色和语速
- **视频素材**: 选择随机或手动选择素材
- **背景音乐**: 配置背景音乐选项
- **封面模板**: 选择封面模板
- **字幕设置**: 配置字体、大小、颜色等
- **视频设置**: 设置分辨率和帧率

### 6. 创建作业

点击"创建批量视频生成作业"按钮提交任务

### 7. 监控进度

在任务管理页面（`/tasks`）查看生成进度

## 文案分配规则

假设有3个账号（A、B、C）和6条文案：

```
文案1 → 账号A
文案2 → 账号B  
文案3 → 账号C
文案4 → 账号A
文案5 → 账号B
文案6 → 账号C
```

## API接口

### 上传Excel文件

```http
POST /api/video-generator/upload-excel
Content-Type: multipart/form-data

file: Excel文件
```

响应：
```json
{
  "success": true,
  "stories": ["文案1", "文案2", "文案3"],
  "total_count": 3,
  "message": "成功解析 3 条文案"
}
```

### 创建批量作业

```http
POST /api/video-generator/batch-jobs
Content-Type: application/json

{
  "name": "批量作业名称",
  "description": "作业描述",
  "config": {
    "voice_settings": {
      "voice": "zh_female_zhixingnvsheng_mars_bigtts",
      "speed": 1.0
    },
    "material_selection": "random",
    "music_selection": "random",
    "cover_template_id": "template_id",
    // ... 其他配置
  },
  "account_ids": ["account1", "account2"],
  "stories": ["文案1", "文案2", "文案3"]
}
```

## 注意事项

1. **Excel格式**: 确保文案内容在第一列，系统会忽略空行
2. **文案长度**: 建议每条文案长度适中，避免过长或过短
3. **账号选择**: 至少选择一个账号
4. **资源配置**: 确保选择的素材、音乐、模板等资源存在
5. **任务监控**: 可在任务管理页面实时查看生成进度

## 技术实现

### 后端实现

- **Excel解析**: 使用pandas库解析Excel文件
- **任务创建**: 扩展现有的VideoGenerationService
- **文案预设**: 在任务创建时预设generated_story字段
- **兼容性**: 完全兼容现有的任务执行流程

### 前端实现

- **文件上传**: 支持拖拽和点击上传
- **实时预览**: 显示解析后的文案列表
- **配置界面**: 复用现有的配置组件
- **进度监控**: 集成到现有的任务管理系统

## 故障排除

### 常见问题

1. **Excel解析失败**
   - 检查文件格式是否为.xlsx或.xls
   - 确保第一列包含文案内容
   - 检查文件是否损坏

2. **任务创建失败**
   - 检查账号是否存在
   - 确保配置参数完整
   - 查看后端日志获取详细错误信息

3. **视频生成失败**
   - 检查TTS服务配置
   - 确保素材和模板资源可用
   - 查看任务日志了解具体错误

### 日志查看

- 后端日志: 查看video_generation_service.py的日志输出
- 任务日志: 在任务管理页面查看具体任务的执行日志
