# Production Environment Configuration
ENVIRONMENT=production

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000

# Security (IMPORTANT: Change this in production!)
SECRET_KEY=change-this-secret-key-in-production-environment

# Database Configuration (Will be set to executable directory)
DATABASE_URL=sqlite:///./reddit_story_generator.db

# File Upload Configuration
MAX_FILE_SIZE=104857600
UPLOAD_DIR=./uploads

# External Tools Configuration
# These paths will be resolved relative to executable
FFMPEG_PATH=./tools/ffmpeg.exe
OUTPUT_FORMAT=mp4
VIDEO_QUALITY=high

# AI Service Configuration (Configure as needed)
OPENAI_API_KEY=
AZURE_OPENAI_ENDPOINT=
AZURE_OPENAI_API_KEY=
F5_TTS_ENDPOINT=http://localhost:5000

# Production Settings
DEBUG=false
LOG_LEVEL=INFO

# Frontend Static Files (Will be bundled with EXE)
FRONTEND_STATIC_DIR=./frontend_dist

# CORS Configuration for standalone EXE
CORS_ORIGINS=*
CORS_ALLOW_CREDENTIALS=true

# Performance Settings
MAX_WORKERS=4
KEEP_ALIVE_TIMEOUT=65
