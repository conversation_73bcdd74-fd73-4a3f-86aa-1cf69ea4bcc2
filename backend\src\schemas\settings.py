"""
设置相关的Pydantic schemas - 对应前端的类型定义
"""

from pydantic import BaseModel, Field
from typing import Optional

# 对应前端的TTSConfig接口
class TTSConfig(BaseModel):
    provider: str = Field(default="coze", description="TTS提供商")
    apiKey: Optional[str] = Field(default="", description="API密钥/Token")
    endpoint: Optional[str] = Field(default="", description="API端点")
    voice: str = Field(default="zh_male_wennuanahu_moon_bigtts", description="说话人ID")
    model: Optional[str] = Field(default="", description="工作流ID")
    speed: float = Field(default=1.2, ge=0.5, le=2.0, description="语速倍率")
    pitch: float = Field(default=1.0, ge=0.25, le=4.0, description="音调")
    volume: float = Field(default=1.0, ge=0.0, le=2.0, description="音量")
    # F5-TTS特有配置
    f5TtsEndpoint: Optional[str] = Field(default="", description="F5-TTS服务端点")

# 对应前端的LLMConfig接口
class LLMConfig(BaseModel):
    provider: str = Field(default="openai", description="LLM提供商")
    apiKey: Optional[str] = Field(default="", description="API密钥")
    endpoint: Optional[str] = Field(default="", description="API端点")
    model: str = Field(default="gpt-3.5-turbo", description="模型")
    temperature: float = Field(default=0.7, ge=0.0, le=2.0, description="温度")
    maxTokens: int = Field(default=2000, ge=1, le=10240, description="最大Token数")
    systemPrompt: Optional[str] = Field(default="", description="系统提示词")

# 对应前端的GeneralSettings接口
class GeneralSettings(BaseModel):
    theme: str = Field(default="light", description="主题")
    language: str = Field(default="zh-CN", description="语言")
    autoSave: bool = Field(default=True, description="自动保存")
    showTips: bool = Field(default=True, description="显示提示")
    outputDirectory: Optional[str] = Field(default="", description="输出目录")

# 完整的设置响应格式
class SettingsResponse(BaseModel):
    tts: TTSConfig
    llm: LLMConfig
    general: GeneralSettings

# 用于部分更新的schemas
class TTSConfigUpdate(BaseModel):
    provider: Optional[str] = None
    apiKey: Optional[str] = None
    endpoint: Optional[str] = None
    voice: Optional[str] = None
    model: Optional[str] = None  # 对于Coze，这里存储workflow_id
    speed: Optional[float] = Field(None, ge=0.5, le=2.0)
    pitch: Optional[float] = Field(None, ge=0.25, le=4.0)
    volume: Optional[float] = Field(None, ge=0.0, le=2.0)
    # F5-TTS特有配置
    f5TtsEndpoint: Optional[str] = None

class LLMConfigUpdate(BaseModel):
    provider: Optional[str] = None
    apiKey: Optional[str] = None
    endpoint: Optional[str] = None
    model: Optional[str] = None
    temperature: Optional[float] = Field(None, ge=0.0, le=2.0)
    maxTokens: Optional[int] = Field(None, ge=1, le=10240)
    systemPrompt: Optional[str] = None

class GeneralSettingsUpdate(BaseModel):
    theme: Optional[str] = None
    language: Optional[str] = None
    autoSave: Optional[bool] = None
    showTips: Optional[bool] = None
    outputDirectory: Optional[str] = None

# 设置更新请求
class SettingsUpdateRequest(BaseModel):
    tts: Optional[TTSConfigUpdate] = None
    llm: Optional[LLMConfigUpdate] = None
    general: Optional[GeneralSettingsUpdate] = None

# 测试请求schemas
class TTSTestRequest(BaseModel):
    text: str = Field(..., min_length=1, max_length=500, description="测试文本")
    config: Optional[TTSConfig] = Field(None, description="TTS配置")

class LLMTestRequest(BaseModel):
    prompt: str = Field(..., min_length=1, max_length=1000, description="测试提示")
    config: Optional[LLMConfig] = Field(None, description="LLM配置")
