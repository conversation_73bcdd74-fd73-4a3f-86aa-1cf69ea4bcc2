#!/usr/bin/env python3
"""
调试音频时长问题的脚本
直接检查音频文件的实际时长，不依赖Whisper
"""

import os
import sys
import logging
import ffmpeg
import json
import subprocess
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent / "backend" / "src"))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_audio_duration_direct(audio_path):
    """直接使用ffprobe获取音频时长，不依赖Whisper"""
    try:
        probe = ffmpeg.probe(str(audio_path))
        format_info = probe.get('format', {})
        duration = float(format_info.get('duration', 0))
        
        logger.info(f"音频文件: {audio_path}")
        logger.info(f"实际时长: {duration:.2f}s")
        
        # 获取更详细的信息
        audio_stream = next((stream for stream in probe['streams'] if stream['codec_type'] == 'audio'), None)
        if audio_stream:
            sample_rate = audio_stream.get('sample_rate', 'N/A')
            channels = audio_stream.get('channels', 'N/A')
            codec = audio_stream.get('codec_name', 'N/A')
            logger.info(f"编码格式: {codec}, 采样率: {sample_rate}, 声道: {channels}")
        
        return duration
    except Exception as e:
        logger.error(f"获取音频时长失败: {e}")
        return None

def get_video_material_durations():
    """获取所有视频素材的时长"""
    materials_dir = Path("backend/uploads/video_materials")
    if not materials_dir.exists():
        logger.error(f"视频素材目录不存在: {materials_dir}")
        return []
    
    materials = []
    total_duration = 0
    
    for file_path in materials_dir.glob("*.mp4"):
        if "thumb_" not in file_path.name:
            try:
                probe = ffmpeg.probe(str(file_path))
                video_stream = next((stream for stream in probe['streams'] if stream['codec_type'] == 'video'), None)
                if video_stream:
                    duration = float(video_stream.get('duration', 0))
                    materials.append({
                        'file': file_path.name,
                        'duration': duration
                    })
                    total_duration += duration
                    logger.info(f"素材: {file_path.name}, 时长: {duration:.2f}s")
            except Exception as e:
                logger.warning(f"跳过文件 {file_path.name}: {e}")
    
    logger.info(f"总共 {len(materials)} 个素材，总时长: {total_duration:.2f}s")
    return materials, total_duration

def check_recent_task_audio():
    """检查最近任务的音频文件"""
    try:
        from database import get_db
        from models.video_generation import VideoGenerationTask
        
        db = next(get_db())
        
        # 获取最近的任务
        recent_task = db.query(VideoGenerationTask).order_by(VideoGenerationTask.created_at.desc()).first()
        
        if not recent_task:
            logger.warning("没有找到任何任务")
            return None
        
        logger.info(f"检查任务 {recent_task.id}")
        logger.info(f"任务状态: {recent_task.status}")
        logger.info(f"创建时间: {recent_task.created_at}")
        
        # 检查音频文件
        if recent_task.audio_file_path:
            audio_path = Path(recent_task.audio_file_path)
            if audio_path.exists():
                actual_duration = get_audio_duration_direct(audio_path)
                
                # 比较Whisper分析结果
                if recent_task.audio_analysis:
                    whisper_duration = recent_task.audio_analysis.get('total_duration', 0)
                    logger.info(f"Whisper分析时长: {whisper_duration:.2f}s")
                    
                    if actual_duration:
                        diff = abs(actual_duration - whisper_duration)
                        logger.info(f"时长差异: {diff:.2f}s")
                        
                        if diff > 1.0:
                            logger.warning(f"⚠️ 时长差异较大: {diff:.2f}s")
                            logger.warning("这可能是导致视频素材不足的原因！")
                        else:
                            logger.info("✅ Whisper分析时长基本准确")
                
                return actual_duration
            else:
                logger.error(f"音频文件不存在: {audio_path}")
        else:
            logger.error("任务没有音频文件路径")
        
        return None
        
    except Exception as e:
        logger.error(f"检查任务失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def simulate_material_selection(audio_duration, materials):
    """模拟素材选择逻辑"""
    logger.info(f"\n=== 模拟素材选择逻辑 ===")
    logger.info(f"目标音频时长: {audio_duration:.2f}s")
    
    # 按时长排序（从长到短）
    sorted_materials = sorted(materials, key=lambda x: x['duration'], reverse=True)
    
    selected_materials = []
    total_selected_duration = 0
    
    for material in sorted_materials:
        selected_materials.append(material)
        total_selected_duration += material['duration']
        logger.info(f"选择素材: {material['file']}, 时长: {material['duration']:.2f}s, 累计: {total_selected_duration:.2f}s")
        
        if total_selected_duration >= audio_duration:
            logger.info(f"✅ 已选择足够的素材，累计时长: {total_selected_duration:.2f}s")
            break
    
    if total_selected_duration < audio_duration:
        shortage = audio_duration - total_selected_duration
        logger.warning(f"⚠️ 素材时长不足，缺少: {shortage:.2f}s")
        logger.warning("这就是导致23秒后黑屏的原因！")
        
        # 计算需要重复使用的素材
        if selected_materials:
            logger.info("建议解决方案：")
            logger.info("1. 添加更多视频素材到素材库")
            logger.info("2. 或者重复使用现有素材")
            
            # 计算需要重复多少次
            cycles_needed = int(shortage / total_selected_duration) + 1
            logger.info(f"3. 需要重复使用素材 {cycles_needed} 次")
    
    return selected_materials, total_selected_duration

def create_audio_duration_fix():
    """创建音频时长获取的修复函数"""
    logger.info("\n=== 创建音频时长获取修复 ===")
    
    fix_code = '''
def get_audio_duration_direct(audio_path):
    """直接获取音频时长，不依赖Whisper"""
    try:
        import ffmpeg
        probe = ffmpeg.probe(str(audio_path))
        format_info = probe.get('format', {})
        return float(format_info.get('duration', 0))
    except Exception as e:
        logger.error(f"获取音频时长失败: {e}")
        return None

# 在视频生成服务中使用：
# audio_duration = get_audio_duration_direct(task.audio_file_path)
# if audio_duration:
#     materials = await helper._select_materials(task, job_config, audio_duration)
'''
    
    logger.info("建议的修复代码：")
    print(fix_code)

async def main():
    """主函数"""
    logger.info("🔍 开始调试音频时长问题...")
    
    # 1. 检查最近任务的音频时长
    logger.info("\n=== 步骤1: 检查最近任务的音频时长 ===")
    actual_audio_duration = check_recent_task_audio()
    
    # 2. 检查视频素材库
    logger.info("\n=== 步骤2: 检查视频素材库 ===")
    materials, total_material_duration = get_video_material_durations()
    
    # 3. 模拟素材选择
    if actual_audio_duration and materials:
        logger.info("\n=== 步骤3: 模拟素材选择 ===")
        selected, selected_duration = simulate_material_selection(actual_audio_duration, materials)
        
        # 4. 分析问题
        logger.info("\n=== 步骤4: 问题分析 ===")
        if selected_duration < actual_audio_duration:
            shortage = actual_audio_duration - selected_duration
            logger.error(f"❌ 发现问题：素材时长不足 {shortage:.2f}s")
            logger.error(f"   音频时长: {actual_audio_duration:.2f}s")
            logger.error(f"   素材时长: {selected_duration:.2f}s")
            logger.error("   这就是导致23秒后黑屏的根本原因！")
            
            # 建议解决方案
            logger.info("\n=== 解决方案建议 ===")
            logger.info("1. 立即方案：重复使用现有素材")
            logger.info("2. 长期方案：添加更多视频素材到素材库")
            logger.info("3. 技术方案：改进素材选择算法，支持素材重复使用")
        else:
            logger.info("✅ 素材时长充足，问题可能在其他地方")
    
    # 5. 提供修复建议
    create_audio_duration_fix()

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
