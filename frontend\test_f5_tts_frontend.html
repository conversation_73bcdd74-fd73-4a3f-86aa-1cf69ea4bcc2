<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>F5-TTS前端集成测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            height: 80px;
            resize: vertical;
        }
    </style>
</head>
<body>
    <h1>🎵 F5-TTS前端集成测试</h1>
    
    <div class="test-section">
        <h2>📡 API连接测试</h2>
        <button onclick="testApiConnection()">测试后端API连接</button>
        <div id="api-result" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>🎤 F5-TTS音色管理测试</h2>
        
        <h3>获取音色列表</h3>
        <button onclick="loadVoices()">加载F5-TTS音色列表</button>
        <div id="voices-result" class="test-result"></div>
        <div id="voices-list"></div>
        
        <h3>创建新音色</h3>
        <form id="create-voice-form">
            <div class="form-group">
                <label for="voice-name">音色名称 *</label>
                <input type="text" id="voice-name" required>
            </div>
            <div class="form-group">
                <label for="voice-description">描述</label>
                <input type="text" id="voice-description">
            </div>
            <div class="form-group">
                <label for="voice-language">语言</label>
                <select id="voice-language">
                    <option value="zh-CN">中文</option>
                    <option value="en-US">英文</option>
                </select>
            </div>
            <div class="form-group">
                <label for="voice-gender">性别</label>
                <select id="voice-gender">
                    <option value="female">女性</option>
                    <option value="male">男性</option>
                </select>
            </div>
            <div class="form-group">
                <label for="voice-ref-text">参考文本 *</label>
                <textarea id="voice-ref-text" placeholder="请输入参考音频文件中说话的内容..." required></textarea>
            </div>
            <div class="form-group">
                <label for="voice-ref-audio">参考音频文件 *</label>
                <input type="file" id="voice-ref-audio" accept="audio/*" required>
            </div>
            <button type="submit">创建音色</button>
        </form>
        <div id="create-result" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>⚙️ 设置集成测试</h2>
        <button onclick="testSettingsIntegration()">测试F5-TTS设置集成</button>
        <div id="settings-result" class="test-result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `test-result ${type}`;
            element.innerHTML = message;
        }
        
        async function testApiConnection() {
            showResult('api-result', '正在测试API连接...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/api/health`);
                if (response.ok) {
                    showResult('api-result', '✅ API连接成功！', 'success');
                } else {
                    showResult('api-result', `❌ API连接失败: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult('api-result', `❌ API连接失败: ${error.message}`, 'error');
            }
        }
        
        async function loadVoices() {
            showResult('voices-result', '正在加载F5-TTS音色列表...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/api/f5-tts-voices`);
                const data = await response.json();
                
                if (response.ok && data.data) {
                    const voices = data.data;
                    showResult('voices-result', `✅ 成功加载 ${voices.length} 个音色`, 'success');
                    
                    // 显示音色列表
                    const voicesList = document.getElementById('voices-list');
                    if (voices.length === 0) {
                        voicesList.innerHTML = '<p>暂无F5-TTS音色</p>';
                    } else {
                        voicesList.innerHTML = voices.map(voice => `
                            <div style="border: 1px solid #ddd; padding: 10px; margin: 10px 0; border-radius: 4px;">
                                <h4>${voice.name}</h4>
                                <p><strong>描述:</strong> ${voice.description || '无'}</p>
                                <p><strong>语言:</strong> ${voice.language} | <strong>性别:</strong> ${voice.gender}</p>
                                <p><strong>参考文本:</strong> ${voice.ref_text}</p>
                                <button onclick="testVoice('${voice.id}')">测试音色</button>
                                <button onclick="deleteVoice('${voice.id}', '${voice.name}')" style="background-color: #dc3545;">删除</button>
                            </div>
                        `).join('');
                    }
                } else {
                    showResult('voices-result', `❌ 加载音色失败: ${data.error || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult('voices-result', `❌ 加载音色失败: ${error.message}`, 'error');
            }
        }
        
        async function testVoice(voiceId) {
            try {
                const formData = new FormData();
                formData.append('test_text', '这是一个音色测试。');
                
                const response = await fetch(`${API_BASE}/api/f5-tts-voices/${voiceId}/test`, {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    alert('✅ 音色测试成功！');
                } else {
                    alert(`❌ 音色测试失败: ${data.error || '未知错误'}`);
                }
            } catch (error) {
                alert(`❌ 音色测试失败: ${error.message}`);
            }
        }
        
        async function deleteVoice(voiceId, voiceName) {
            if (!confirm(`确定要删除音色"${voiceName}"吗？`)) {
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/api/f5-tts-voices/${voiceId}`, {
                    method: 'DELETE'
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    alert('✅ 音色删除成功！');
                    loadVoices(); // 重新加载列表
                } else {
                    alert(`❌ 删除音色失败: ${data.error || '未知错误'}`);
                }
            } catch (error) {
                alert(`❌ 删除音色失败: ${error.message}`);
            }
        }
        
        document.getElementById('create-voice-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            showResult('create-result', '正在创建音色...', 'info');
            
            const formData = new FormData();
            formData.append('name', document.getElementById('voice-name').value);
            formData.append('description', document.getElementById('voice-description').value);
            formData.append('language', document.getElementById('voice-language').value);
            formData.append('gender', document.getElementById('voice-gender').value);
            formData.append('ref_text', document.getElementById('voice-ref-text').value);
            formData.append('ref_audio', document.getElementById('voice-ref-audio').files[0]);
            
            try {
                const response = await fetch(`${API_BASE}/api/f5-tts-voices`, {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (response.ok && data.data) {
                    showResult('create-result', '✅ 音色创建成功！', 'success');
                    document.getElementById('create-voice-form').reset();
                    loadVoices(); // 重新加载列表
                } else {
                    showResult('create-result', `❌ 创建音色失败: ${data.error || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult('create-result', `❌ 创建音色失败: ${error.message}`, 'error');
            }
        });
        
        async function testSettingsIntegration() {
            showResult('settings-result', '正在测试设置集成...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/api/settings`);
                const data = await response.json();
                
                if (response.ok && data.data && data.data.tts) {
                    const ttsConfig = data.data.tts;
                    if ('f5TtsEndpoint' in ttsConfig) {
                        showResult('settings-result', '✅ 设置集成成功！F5-TTS配置字段存在', 'success');
                    } else {
                        showResult('settings-result', '❌ 设置集成失败：缺少F5-TTS配置字段', 'error');
                    }
                } else {
                    showResult('settings-result', `❌ 获取设置失败: ${data.error || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult('settings-result', `❌ 设置集成测试失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时自动测试API连接
        window.addEventListener('load', () => {
            testApiConnection();
        });
    </script>
</body>
</html>
