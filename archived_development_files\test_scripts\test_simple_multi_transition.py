#!/usr/bin/env python3
"""
简单的多段转场测试 - 验证修复后的offset计算
"""

import sys
import os
import logging
from pathlib import Path
import ffmpeg
import tempfile
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.video_generation_helpers import VideoCompositionService

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_simple_multi_transition():
    """测试简单的3段转场"""
    
    logger.info("🎬 开始测试简单的3段转场...")
    
    try:
        # 创建3个简单的测试视频
        colors = [
            ('red', '#FF0000', 3.0),
            ('green', '#00FF00', 4.0), 
            ('blue', '#0000FF', 2.0)
        ]
        
        test_videos = []
        durations = []
        
        for name, color, duration in colors:
            output_path = f"simple_{name}.mp4"
            if not Path(output_path).exists():
                logger.info(f"创建{duration}s {name}视频")
                (
                    ffmpeg
                    .input(f'color={color}:size=640x480:duration={duration}:rate=30', f='lavfi')
                    .output(output_path, vcodec='libx264', pix_fmt='yuv420p')
                    .overwrite_output()
                    .run(quiet=True)
                )
            
            test_videos.append(output_path)
            durations.append(duration)
        
        logger.info(f"视频片段: {test_videos}")
        logger.info(f"时长: {durations}")
        logger.info(f"总时长: {sum(durations)}s")
        
        # 测试转场
        transition_duration = 0.5
        expected_duration = sum(durations) - (len(durations) - 1) * transition_duration
        logger.info(f"预期转场后时长: {expected_duration}s")
        
        # 创建视频流
        streams = []
        for video_path in test_videos:
            stream = ffmpeg.input(video_path)
            streams.append(stream)
        
        logger.info("应用转场效果...")
        
        # 使用修复后的转场方法
        final_stream = VideoCompositionService._create_video_with_transitions(
            streams, 'fade', transition_duration, durations
        )
        
        # 输出视频
        out = ffmpeg.output(
            final_stream,
            'simple_multi_transition.mp4',
            vcodec='libx264',
            preset='fast',
            pix_fmt='yuv420p'
        ).overwrite_output()
        
        logger.info("执行FFmpeg命令...")
        
        import subprocess
        cmd = ffmpeg.compile(out)
        
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        stdout, stderr = process.communicate(timeout=60)
        
        if process.returncode == 0:
            if Path('simple_multi_transition.mp4').exists():
                file_size = Path('simple_multi_transition.mp4').stat().st_size
                logger.info("✅ 简单多段转场测试成功!")
                logger.info(f"文件大小: {file_size} bytes")
                logger.info(f"预期时长: {expected_duration}s")
                logger.info("预期效果: 红色(3s) → 绿色(4s) → 蓝色(2s) 用0.5s转场")
                logger.info("请检查视频是否完整播放，没有定格问题")
                return True
            else:
                logger.error("❌ 输出文件不存在")
                return False
        else:
            logger.error(f"❌ FFmpeg执行失败，返回码: {process.returncode}")
            if stderr:
                stderr_text = stderr.decode('utf-8', errors='ignore')
                logger.error(f"stderr: {stderr_text[:500]}...")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False
    
    finally:
        # 清理测试文件
        for name in ['red', 'green', 'blue']:
            file_path = f"simple_{name}.mp4"
            if Path(file_path).exists():
                Path(file_path).unlink()

if __name__ == "__main__":
    logger.info("🚀 开始简单多段转场测试")
    
    success = test_simple_multi_transition()
    
    if success:
        logger.info("\n🎉 简单多段转场测试成功!")
        logger.info("offset计算修复成功！")
        logger.info("现在可以测试更多段的转场了。")
    else:
        logger.error("\n❌ 简单多段转场测试失败")
        sys.exit(1)
