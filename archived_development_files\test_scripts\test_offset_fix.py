#!/usr/bin/env python3
"""
测试offset修复 - 验证11-12秒定格问题是否解决
"""

import sys
import os
import logging
from pathlib import Path
import ffmpeg
import subprocess

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.video_generation_helpers import VideoCompositionService

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_offset_fix():
    """测试offset修复"""
    
    logger.info("🎬 测试offset修复 - 目标解决11-12秒定格问题")
    
    try:
        # 创建4个测试视频，总时长约15秒，确保能测试到11-12秒
        video_configs = [
            ('red', '#FF0000', 4.0),      # 4秒红色
            ('green', '#00FF00', 3.0),    # 3秒绿色  
            ('blue', '#0000FF', 4.0),     # 4秒蓝色
            ('yellow', '#FFFF00', 3.0),   # 3秒黄色
        ]
        
        test_files = []
        durations = []
        
        for name, color, duration in video_configs:
            filename = f"offset_test_{name}.mp4"
            logger.info(f"创建 {filename} ({duration}s)")
            
            (
                ffmpeg
                .input(f'color={color}:size=640x480:duration={duration}:rate=30', f='lavfi')
                .output(filename, vcodec='libx264', pix_fmt='yuv420p')
                .overwrite_output()
                .run(quiet=True)
            )
            test_files.append(filename)
            durations.append(duration)
        
        logger.info(f"视频文件: {test_files}")
        logger.info(f"时长: {durations}")
        logger.info(f"总原始时长: {sum(durations)}s")
        
        # 转场配置
        transition_duration = 0.8
        expected_duration = sum(durations) - (len(durations) - 1) * transition_duration
        logger.info(f"预期转场后时长: {expected_duration}s")
        logger.info(f"关键测试点: 11-12秒应该正常播放，不定格")
        
        # 创建视频流
        streams = []
        for video_path in test_files:
            stream = ffmpeg.input(video_path)
            streams.append(stream)
        
        logger.info("应用修复后的转场效果...")
        
        # 使用修复后的转场方法
        final_stream = VideoCompositionService._create_video_with_transitions(
            streams, 'fade', transition_duration, durations
        )
        
        # 输出视频
        out = ffmpeg.output(
            final_stream,
            'offset_fix_test.mp4',
            vcodec='libx264',
            preset='fast',
            pix_fmt='yuv420p'
        ).overwrite_output()
        
        # 获取并显示命令
        cmd = ffmpeg.compile(out)
        logger.info("生成的FFmpeg命令:")
        logger.info(' '.join(cmd))
        
        # 执行命令
        logger.info("执行FFmpeg命令...")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            if Path('offset_fix_test.mp4').exists():
                file_size = Path('offset_fix_test.mp4').stat().st_size
                logger.info("✅ offset修复测试成功!")
                logger.info(f"文件大小: {file_size} bytes")
                logger.info(f"预期时长: {expected_duration}s")
                
                logger.info("\n🎬 预期播放效果:")
                logger.info("0-4s: 红色")
                logger.info("4-7s: 绿色 (有0.8s转场重叠)")
                logger.info("7-11s: 蓝色 (有0.8s转场重叠)")
                logger.info("11-14s: 黄色 (有0.8s转场重叠)")
                
                logger.info("\n🔍 关键验证点:")
                logger.info("- 11-12秒: 应该显示黄色，不应该定格")
                logger.info("- 12-13秒: 应该继续显示黄色")
                logger.info("- 视频应该完整播放到结束")
                logger.info("- 所有转场应该平滑自然")
                
                return True
            else:
                logger.error("❌ 输出文件不存在")
                return False
        else:
            logger.error(f"❌ FFmpeg执行失败，返回码: {result.returncode}")
            logger.error(f"stderr: {result.stderr}")
            
            # 分析错误
            if "offset" in result.stderr.lower():
                logger.error("⚠️ offset参数仍有问题")
            elif "xfade" in result.stderr.lower():
                logger.error("⚠️ xfade滤镜配置问题")
            
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False
    
    finally:
        # 清理测试文件
        for name, _, _ in video_configs:
            filename = f"offset_test_{name}.mp4"
            if Path(filename).exists():
                Path(filename).unlink()

if __name__ == "__main__":
    logger.info("🚀 开始offset修复测试")
    logger.info("目标: 解决11-12秒定格问题")
    
    success = test_offset_fix()
    
    if success:
        logger.info("\n🎉 offset修复测试成功!")
        logger.info("11-12秒定格问题应该已经解决！")
        logger.info("请播放 offset_fix_test.mp4 验证效果")
    else:
        logger.error("\n❌ offset修复测试失败")
        sys.exit(1)
