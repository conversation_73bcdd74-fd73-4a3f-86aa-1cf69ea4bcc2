#!/usr/bin/env python3
"""
简单封面叠加测试脚本
直接使用FFmpeg命令测试封面叠加
"""

import sys
import os
from pathlib import Path
import ffmpeg
from loguru import logger

def test_simple_cover_overlay(video_path: str, cover_path: str, output_path: str):
    """
    简单测试封面叠加功能
    """
    try:
        # 检查输入文件
        if not Path(video_path).exists():
            logger.error(f"视频文件不存在: {video_path}")
            return False
            
        if not Path(cover_path).exists():
            logger.error(f"封面文件不存在: {cover_path}")
            return False
        
        logger.info(f"开始测试封面叠加")
        logger.info(f"视频: {video_path}")
        logger.info(f"封面: {cover_path}")
        logger.info(f"输出: {output_path}")
        
        # 获取视频信息
        probe = ffmpeg.probe(video_path)
        video_info = next(s for s in probe['streams'] if s['codec_type'] == 'video')
        width = int(video_info['width'])
        height = int(video_info['height'])
        duration = float(video_info['duration'])
        
        logger.info(f"视频信息: {width}x{height}, 时长: {duration}s")
        
        # 计算封面大小和位置
        cover_width = int(width * 0.8)
        cover_x = '(main_w-overlay_w)/2'  # 居中
        cover_y = '(main_h-overlay_h)/2'  # 居中
        cover_duration = min(5.0, duration)  # 显示5秒或视频总时长
        
        logger.info(f"封面配置: 宽度={cover_width}, 位置=居中, 时长={cover_duration}s")
        
        # 创建FFmpeg流
        video_input = ffmpeg.input(video_path)
        cover_input = ffmpeg.input(cover_path)
        
        # 缩放封面
        cover_scaled = cover_input.filter('scale', cover_width, -1)
        
        # 叠加封面到视频
        output = video_input.overlay(
            cover_scaled,
            x=cover_x,
            y=cover_y,
            enable=f'between(t,0,{cover_duration})'
        )
        
        # 输出视频
        out = ffmpeg.output(
            output, 
            output_path,
            vcodec='libx264',
            preset='fast',
            pix_fmt='yuv420p'
        ).overwrite_output()
        
        logger.info("开始执行FFmpeg命令...")
        
        # 显示FFmpeg命令
        cmd = ffmpeg.compile(out)
        logger.info(f"FFmpeg命令: {' '.join(cmd)}")
        
        # 执行
        ffmpeg.run(out, quiet=False)
        
        # 检查结果
        if Path(output_path).exists():
            file_size = Path(output_path).stat().st_size
            logger.info(f"✅ 成功! 输出文件: {output_path}, 大小: {file_size} bytes")
            return True
        else:
            logger.error("❌ 失败: 输出文件不存在")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_fade(video_path: str, cover_path: str, output_path: str):
    """
    测试带淡入淡出效果的封面叠加
    """
    try:
        logger.info("\n=== 测试淡入淡出效果 ===")
        
        # 获取视频信息
        probe = ffmpeg.probe(video_path)
        video_info = next(s for s in probe['streams'] if s['codec_type'] == 'video')
        width = int(video_info['width'])
        height = int(video_info['height'])
        duration = float(video_info['duration'])
        
        # 计算参数
        cover_width = int(width * 0.8)
        cover_duration = min(5.0, duration)
        fade_duration = 0.5
        
        # 创建流
        video_input = ffmpeg.input(video_path)
        cover_input = ffmpeg.input(cover_path)
        
        # 处理封面：缩放 + 淡入淡出
        cover_scaled = cover_input.filter('scale', cover_width, -1)
        cover_with_fade = cover_scaled.filter(
            'fade', 
            type='in', 
            start_time=0, 
            duration=fade_duration
        ).filter(
            'fade',
            type='out', 
            start_time=cover_duration - fade_duration,
            duration=fade_duration
        )
        
        # 叠加
        output = video_input.overlay(
            cover_with_fade,
            x='(main_w-overlay_w)/2',
            y='(main_h-overlay_h)/2',
            enable=f'between(t,0,{cover_duration})'
        )
        
        # 输出
        fade_output = output_path.replace('.mp4', '_fade.mp4')
        out = ffmpeg.output(
            output,
            fade_output,
            vcodec='libx264',
            preset='fast',
            pix_fmt='yuv420p'
        ).overwrite_output()
        
        logger.info("执行淡入淡出测试...")
        ffmpeg.run(out, quiet=False)
        
        if Path(fade_output).exists():
            logger.info(f"✅ 淡入淡出测试成功: {fade_output}")
            return True
        else:
            logger.error("❌ 淡入淡出测试失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 淡入淡出测试失败: {e}")
        return False

def main():
    """主函数"""
    if len(sys.argv) != 4:
        print("用法: python test_simple_cover.py <视频文件> <封面文件> <输出文件>")
        print("示例: python test_simple_cover.py input.mp4 cover.jpg output.mp4")
        print("\n这个脚本会生成两个测试文件:")
        print("1. output.mp4 - 基础封面叠加")
        print("2. output_fade.mp4 - 带淡入淡出效果")
        sys.exit(1)
    
    video_path = sys.argv[1]
    cover_path = sys.argv[2]
    output_path = sys.argv[3]
    
    # 配置日志
    logger.remove()
    logger.add(sys.stdout, level="INFO", format="<green>{time:HH:mm:ss}</green> | <level>{level}</level> | {message}")
    
    # 运行测试
    logger.info("开始封面叠加测试...")
    
    # 基础测试
    success1 = test_simple_cover_overlay(video_path, cover_path, output_path)
    
    # 淡入淡出测试
    success2 = test_with_fade(video_path, cover_path, output_path)
    
    if success1 and success2:
        logger.info("\n🎉 所有测试都成功!")
    else:
        logger.error("\n❌ 部分测试失败")

if __name__ == "__main__":
    main()
