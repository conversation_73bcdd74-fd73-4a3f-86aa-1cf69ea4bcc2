'use client'

import React, { useState, useEffect } from 'react'
import { 
  PlayIcon, 
  CogIcon, 
  RectangleStackIcon, 
  SpeakerWaveIcon,
  ChatBubbleBottomCenterTextIcon,
  PhotoIcon,
  UserGroupIcon,
  FolderIcon,
  EyeIcon,
  PlusIcon,
  XMarkIcon,
  ArrowPathIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import apiService, { 
  Account, 
  VideoMaterial, 
  VideoCategory,
  BackgroundMusic, 
  Prompt, 
  CoverTemplate,
  CreateVideoGenerationJobRequest,
  AccountConfig,
  TTSVoice
} from '../../services/apiService'
import { ttsVoices, getTTSVoices } from '../../config/ttsConfig'
import { useSettingsStore } from '../../store/settingsStore'
import { useRouter } from 'next/navigation'

interface LoadingState {
  accounts: boolean
  materials: boolean
  music: boolean
  prompts: boolean
  templates: boolean
}

interface ErrorState {
  accounts?: string
  materials?: string
  music?: string
  prompts?: string
  templates?: string
}

export default function GeneratePage() {
  const router = useRouter()
  
  // Loading and error states
  const [loading, setLoading] = useState<LoadingState>({
    accounts: true,
    materials: true,
    music: true,
    prompts: true,
    templates: true,
  })
  const [errors, setErrors] = useState<ErrorState>({})
  
  // Data states
  const [accounts, setAccounts] = useState<Account[]>([])
  const [materialCategories, setMaterialCategories] = useState<VideoCategory[]>([])
  const [materials, setMaterials] = useState<VideoMaterial[]>([])
  const [musicCategories, setMusicCategories] = useState<string[]>([])
  const [musicList, setMusicList] = useState<BackgroundMusic[]>([])
  const [promptCategories, setPromptCategories] = useState<string[]>([])
  const [prompts, setPrompts] = useState<Prompt[]>([])
  const [templates, setTemplates] = useState<CoverTemplate[]>([])
  
  // Form states
  const [jobName, setJobName] = useState('')
  const [jobDescription, setJobDescription] = useState('')
  
  // Material configuration
  const [materialSelectionMode, setMaterialSelectionMode] = useState<'random' | 'manual'>('random')
  const [selectedMaterialCategory, setSelectedMaterialCategory] = useState('')
  const [selectedMaterials, setSelectedMaterials] = useState<string[]>([])
  
  // Prompt configuration
  const [selectedPromptCategory, setSelectedPromptCategory] = useState('')
  const [selectedPrompt, setSelectedPrompt] = useState('')
  
  // Audio configuration
  const [selectedVoice, setSelectedVoice] = useState('')
  const [speechRate, setSpeechRate] = useState(1.2)
  const [speechVolume, setSpeechVolume] = useState(100) // 语音音量百分比
  const [backgroundMusicVolume, setBackgroundMusicVolume] = useState(5) // 背景音乐音量百分比
  const [enableBackgroundMusic, setEnableBackgroundMusic] = useState(true) // 是否启用背景音乐
  const [dynamicTtsVoices, setDynamicTtsVoices] = useState<TTSVoice[]>([]) // 动态加载的音色列表
  
  // Music configuration
  const [musicSelectionMode, setMusicSelectionMode] = useState<'random' | 'specific'>('random')
  const [selectedMusicCategory, setSelectedMusicCategory] = useState('')
  const [selectedMusic, setSelectedMusic] = useState('')
  
  // Cover template configuration
  const [selectedTemplate, setSelectedTemplate] = useState('')
  const [coverPosition, setCoverPosition] = useState<'top' | 'center' | 'bottom' | 'custom'>('center')
  const [coverAnimation, setCoverAnimation] = useState('fade_in_out')
  const [customCoverX, setCustomCoverX] = useState('')
  const [customCoverY, setCustomCoverY] = useState('')
  const [animationDuration, setAnimationDuration] = useState(0.5)
  
  // Subtitle configuration
  const [subtitleFont, setSubtitleFont] = useState('ZY Starry')
  const [availableFonts, setAvailableFonts] = useState<{name: string, family: string, category: string, available: boolean}[]>([])
  const [fontsLoaded, setFontsLoaded] = useState(false)
  const [showAllFonts, setShowAllFonts] = useState(false) // 是否显示所有字体（包括不可用的）
  const [useCustomFont, setUseCustomFont] = useState(false) // 是否使用自定义字体
  const [customFontName, setCustomFontName] = useState('') // 自定义字体名称

  // Transition configuration
  const [enableTransitions, setEnableTransitions] = useState(false)
  const [transitionType, setTransitionType] = useState('fade')
  const [transitionDuration, setTransitionDuration] = useState(0.5)
  const [subtitleSize, setSubtitleSize] = useState(24)
  const [subtitleColor, setSubtitleColor] = useState('#FFFFFF')
  const [subtitlePosition, setSubtitlePosition] = useState<'top' | 'center' | 'bottom'>('center')
  const [wordsPerScreen, setWordsPerScreen] = useState(1) // 每屏单词数，默认1
  const [strokeThickness, setStrokeThickness] = useState(10) // 描边厚度，默认10px
  const [strokeColor, setStrokeColor] = useState('#000000') // 描边颜色，默认黑色
  
  // Video configuration
  const [videoResolution, setVideoResolution] = useState('1080x1920')
  const [videoFps, setVideoFps] = useState(30)
  
  // Account configuration
  const [accountConfigs, setAccountConfigs] = useState<AccountConfig[]>([])
  const [materialsLoading, setMaterialsLoading] = useState(false)
  const [musicLoading, setMusicLoading] = useState(false)
  
  // UI states
  const [showPreview, setShowPreview] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Settings store
  const { tts, loadFromBackend } = useSettingsStore()

  // Load initial data
  useEffect(() => {
    loadAllData()
    // 加载设置配置
    loadFromBackend()
  }, [])

  const loadAllData = async () => {
    setLoading({
      accounts: true,
      materials: true,
      music: true,
      prompts: true,
      templates: true,
    })
    setErrors({})

    // Load accounts
    const accountsResponse = await apiService.accounts.getAccounts()
    if (accountsResponse.error) {
      setErrors(prev => ({ ...prev, accounts: accountsResponse.error }))
    } else {
      setAccounts(accountsResponse.data || [])
    }
    setLoading(prev => ({ ...prev, accounts: false }))

    // Load material categories
    const materialCategoriesResponse = await apiService.videoMaterials.getCategories()
    if (materialCategoriesResponse.error) {
      setErrors(prev => ({ ...prev, materials: materialCategoriesResponse.error }))
    } else {
      setMaterialCategories(materialCategoriesResponse.data || [])
    }
    setLoading(prev => ({ ...prev, materials: false }))

    // Load music categories
    const musicCategoriesResponse = await apiService.backgroundMusic.getCategories()
    if (musicCategoriesResponse.error) {
      setErrors(prev => ({ ...prev, music: musicCategoriesResponse.error }))
    } else {
      setMusicCategories(musicCategoriesResponse.data || [])
    }
    setLoading(prev => ({ ...prev, music: false }))

    // Load prompt categories
    const promptCategoriesResponse = await apiService.prompts.getCategories()
    if (promptCategoriesResponse.error) {
      setErrors(prev => ({ ...prev, prompts: promptCategoriesResponse.error }))
    } else {
      setPromptCategories(promptCategoriesResponse.data || [])
    }
    setLoading(prev => ({ ...prev, prompts: false }))

    // Load templates
    const templatesResponse = await apiService.coverTemplates.getTemplates()
    if (templatesResponse.error) {
      setErrors(prev => ({ ...prev, templates: templatesResponse.error }))
    } else {
      setTemplates(templatesResponse.data || [])
    }
    setLoading(prev => ({ ...prev, templates: false }))
    
    // Load available fonts
    await loadAvailableFonts()
  }

  // Load materials when category or mode changes
  useEffect(() => {
    async function load() {
      if (selectedMaterialCategory && materialSelectionMode === 'manual') {
        setMaterialsLoading(true);
        await loadMaterials(selectedMaterialCategory);
        setMaterialsLoading(false);
      } else {
        setMaterials([]); // Clear materials if not in manual mode
      }
    }
    load();
    setSelectedMaterials([]); // Reset selection when category or mode changes
  }, [selectedMaterialCategory, materialSelectionMode]);

  // Load music when category or mode changes
  useEffect(() => {
    async function load() {
      if (selectedMusicCategory && musicSelectionMode === 'specific') {
        setMusicLoading(true);
        await loadMusic(selectedMusicCategory);
        setMusicLoading(false);
      } else {
        setMusicList([]); // Clear music list if not in specific mode
      }
    }
    load();
    setSelectedMusic(''); // Reset selection when category or mode changes
  }, [selectedMusicCategory, musicSelectionMode]);''

  // Load prompts when category changes
  useEffect(() => {
    if (selectedPromptCategory) {
      loadPrompts(selectedPromptCategory)
    }
  }, [selectedPromptCategory])

  const loadMaterials = async (category: string) => {
    const response = await apiService.videoMaterials.getMaterials(category)
    if (response.data) {
      setMaterials(response.data)
    }
  }

  const loadMusic = async (category: string) => {
    const response = await apiService.backgroundMusic.getMusic(category)
    if (response.data) {
      setMusicList(response.data)
    }
  }

  const loadPrompts = async (category: string) => {
    const response = await apiService.prompts.getPrompts(category)
    if (response.data) {
      setPrompts(response.data)
      // Auto-select first prompt
      if (response.data.length > 0) {
        setSelectedPrompt(response.data[0].id)
      }
    }
  }

  // Font detection and management
  const predefinedFonts = [
    // 专用字体
    { name: 'ZY Starry', family: 'ZY Starry', category: '装饰性' },
    { name: 'ZY Flexible', family: 'FSP DEMO - Flexible Variable', category: '装饰性' },
    
    // 中文字体 - 添加更多变体名称
    { name: '微软雅黑', family: 'Microsoft YaHei', category: '中文' },
    { name: '微软雅黑 (备用)', family: '微软雅黑', category: '中文' },
    { name: '黑体', family: 'SimHei', category: '中文' },
    { name: '黑体 (备用)', family: '黑体', category: '中文' },
    { name: '宋体', family: 'SimSun', category: '中文' },
    { name: '宋体 (备用)', family: '宋体', category: '中文' },
    { name: '楷体', family: 'KaiTi', category: '中文' },
    { name: '楷体 (备用)', family: '楷体', category: '中文' },
    { name: '仿宋', family: 'FangSong', category: '中文' },
    { name: '仿宋 (备用)', family: '仿宋', category: '中文' },
    { name: '华文黑体', family: 'STHeiti', category: '中文' },
    { name: '华文楷体', family: 'STKaiti', category: '中文' },
    { name: '华文宋体', family: 'STSong', category: '中文' },
    { name: '华文仿宋', family: 'STFangsong', category: '中文' },
    { name: '华文中宋', family: 'STZhongsong', category: '中文' },
    { name: '华文彩云', family: 'STCaiyun', category: '中文' },
    { name: '华文琥珀', family: 'STHupo', category: '中文' },
    { name: '华文新魏', family: 'STXinwei', category: '中文' },
    { name: '华文隶书', family: 'STLiti', category: '中文' },
    { name: '华文行楷', family: 'STXingkai', category: '中文' },
    { name: '方正舒体', family: 'FZShuTi', category: '中文' },
    { name: '方正姚体', family: 'FZYaoti', category: '中文' },
    { name: '思源黑体', family: 'Source Han Sans CN', category: '中文' },
    { name: '思源宋体', family: 'Source Han Serif CN', category: '中文' },
    { name: '思源黑体 (备用)', family: 'Noto Sans CJK SC', category: '中文' },
    { name: '思源宋体 (备用)', family: 'Noto Serif CJK SC', category: '中文' },
    
    // 英文字体
    { name: 'Arial', family: 'Arial', category: '英文' },
    { name: 'Arial Black', family: 'Arial Black', category: '英文' },
    { name: 'Arial Narrow', family: 'Arial Narrow', category: '英文' },
    { name: 'Helvetica', family: 'Helvetica', category: '英文' },
    { name: 'Helvetica Neue', family: 'Helvetica Neue', category: '英文' },
    { name: 'Times New Roman', family: 'Times New Roman', category: '英文' },
    { name: 'Times', family: 'Times', category: '英文' },
    { name: 'Georgia', family: 'Georgia', category: '英文' },
    { name: 'Verdana', family: 'Verdana', category: '英文' },
    { name: 'Trebuchet MS', family: 'Trebuchet MS', category: '英文' },
    { name: 'Comic Sans MS', family: 'Comic Sans MS', category: '英文' },
    { name: 'Impact', family: 'Impact', category: '英文' },
    { name: 'Courier New', family: 'Courier New', category: '英文' },
    { name: 'Courier', family: 'Courier', category: '英文' },
    { name: 'Palatino', family: 'Palatino', category: '英文' },
    { name: 'Book Antiqua', family: 'Book Antiqua', category: '英文' },
    { name: 'Century Gothic', family: 'Century Gothic', category: '英文' },
    { name: 'Lucida Console', family: 'Lucida Console', category: '英文' },
    { name: 'Tahoma', family: 'Tahoma', category: '英文' },
    { name: 'Calibri', family: 'Calibri', category: '英文' },
    { name: 'Cambria', family: 'Cambria', category: '英文' },
    { name: 'Consolas', family: 'Consolas', category: '英文' },
    
    // 系统字体
    { name: 'System UI', family: 'system-ui', category: '系统' },
    { name: 'Sans Serif', family: 'sans-serif', category: '系统' },
    { name: 'Serif', family: 'serif', category: '系统' },
    { name: 'Monospace', family: 'monospace', category: '系统' },
    { name: 'Cursive', family: 'cursive', category: '系统' },
    { name: 'Fantasy', family: 'fantasy', category: '系统' },
  ]

  const checkFontAvailability = (fontFamily: string): boolean => {
    try {
      // 方法1: 使用document.fonts API (现代浏览器支持)
      if ('fonts' in document) {
        // 检查字体是否在字体集中
        const fontFace = new FontFace('test-font', `local("${fontFamily}")`)
        try {
          // 尝试加载字体
          fontFace.load().then(() => {
            console.log(`字体 ${fontFamily} 可用`)
          }).catch(() => {
            console.log(`字体 ${fontFamily} 不可用`)
          })
        } catch (e) {
          // FontFace构造失败，继续使用Canvas方法
        }
      }

      // 方法2: 改进的Canvas检测方法
      const testStrings = [
        'mmmmmmmmmmlli',      // 原始测试字符串
        '中文测试字体效果',      // 中文字符
        'ABCDEFGHijklmn',     // 英文字符
        '0123456789'          // 数字字符
      ]
      
      const fontSize = '48px'
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      if (!ctx) return true // 如果无法创建context，假设字体可用

      canvas.width = 300
      canvas.height = 100

      // 测试多个fallback字体
      const fallbackFonts = ['monospace', 'serif', 'sans-serif']
      
      for (const testString of testStrings) {
        for (const fallback of fallbackFonts) {
          // 清除画布
          ctx.clearRect(0, 0, canvas.width, canvas.height)
          ctx.textAlign = 'left'
          ctx.fillStyle = 'black'
          ctx.textBaseline = 'top'

          // 绘制使用 fallback 字体的文本
          ctx.font = `${fontSize} ${fallback}`
          ctx.fillText(testString, 10, 10)
          const fallbackData = ctx.getImageData(0, 0, canvas.width, canvas.height)

          // 清除画布
          ctx.clearRect(0, 0, canvas.width, canvas.height)

          // 绘制使用目标字体的文本
          ctx.font = `${fontSize} "${fontFamily}", ${fallback}`
          ctx.fillText(testString, 10, 10)
          const testData = ctx.getImageData(0, 0, canvas.width, canvas.height)

          // 比较图像数据
          let diffCount = 0
          const totalPixels = fallbackData.data.length / 4
          
          for (let i = 0; i < fallbackData.data.length; i += 4) {
            if (fallbackData.data[i] !== testData.data[i] || 
                fallbackData.data[i + 1] !== testData.data[i + 1] || 
                fallbackData.data[i + 2] !== testData.data[i + 2] || 
                fallbackData.data[i + 3] !== testData.data[i + 3]) {
              diffCount++
            }
          }

          // 如果差异像素超过1%，认为字体不同（即目标字体可用）
          const diffPercentage = diffCount / totalPixels
          if (diffPercentage > 0.01) {
            return true
          }
        }
      }

      return false // 所有测试都相同，字体不可用
    } catch (error) {
      console.warn('字体检测失败:', fontFamily, error)
      return true // 如果检测失败，假设字体可用
    }
  }

  const loadAvailableFonts = async () => {
    try {
      console.log('开始检测字体...')
      
      const fontsWithAvailability = predefinedFonts.map(font => {
        const available = checkFontAvailability(font.family)
        console.log(`字体检测: ${font.name} (${font.family}) - ${available ? '可用' : '不可用'}`)
        return {
          ...font,
          available
        }
      })

      // 按类别和可用性排序
      fontsWithAvailability.sort((a, b) => {
        // 首先按可用性排序（可用的在前）
        if (a.available && !b.available) return -1
        if (!a.available && b.available) return 1
        
        // 然后按类别排序
        const categoryOrder = ['装饰性', '中文', '英文', '系统']
        const aCategoryIndex = categoryOrder.indexOf(a.category)
        const bCategoryIndex = categoryOrder.indexOf(b.category)
        if (aCategoryIndex !== bCategoryIndex) {
          return aCategoryIndex - bCategoryIndex
        }
        
        // 最后按名称排序
        return a.name.localeCompare(b.name)
      })

      setAvailableFonts(fontsWithAvailability)
      setFontsLoaded(true)
      
      const availableCount = fontsWithAvailability.filter(f => f.available).length
      console.log(`字体检测完成: ${availableCount}/${fontsWithAvailability.length} 个字体可用`)
      
      // 输出详细检测结果
      const availableFontsByCategory = fontsWithAvailability.reduce((acc, font) => {
        if (!acc[font.category]) acc[font.category] = { available: 0, total: 0 }
        acc[font.category].total++
        if (font.available) acc[font.category].available++
        return acc
      }, {} as Record<string, {available: number, total: number}>)
      
      console.log('各类别字体检测结果:', availableFontsByCategory)
      
    } catch (error) {
      console.error('加载字体失败:', error)
      // 如果检测失败，使用默认字体列表（全部标记为可用）
      const fallbackFonts = predefinedFonts.map(font => ({ ...font, available: true }))
      setAvailableFonts(fallbackFonts)
      setFontsLoaded(true)
    }
  }

  // Auto-select first options when data loads
  useEffect(() => {
    const safeMaterialCategories = Array.isArray(materialCategories) ? materialCategories : []
    if (safeMaterialCategories.length > 0 && !selectedMaterialCategory) {
      setSelectedMaterialCategory(safeMaterialCategories[0].name)
    }
  }, [materialCategories, selectedMaterialCategory])

  useEffect(() => {
    const safeMusicCategories = Array.isArray(musicCategories) ? musicCategories : []
    if (safeMusicCategories.length > 0 && !selectedMusicCategory) {
      setSelectedMusicCategory(safeMusicCategories[0])
    }
  }, [musicCategories, selectedMusicCategory])

  useEffect(() => {
    const safePromptCategories = Array.isArray(promptCategories) ? promptCategories : []
    if (safePromptCategories.length > 0 && !selectedPromptCategory) {
      setSelectedPromptCategory(safePromptCategories[0])
    }
  }, [promptCategories, selectedPromptCategory])

  useEffect(() => {
    const safeTemplates = Array.isArray(templates) ? templates : []
    if (safeTemplates.length > 0 && !selectedTemplate) {
      setSelectedTemplate(safeTemplates[0].id)
    }
  }, [templates, selectedTemplate])

  useEffect(() => {
    const safeTtsVoices = Array.isArray(ttsVoices) ? ttsVoices : []
    if (safeTtsVoices.length > 0 && !selectedVoice) {
      setSelectedVoice(safeTtsVoices[0].id)
    }
  }, [ttsVoices, selectedVoice])

  // 动态加载音色列表
  useEffect(() => {
    const loadDynamicVoices = async () => {
      if (tts.provider) {
        try {
          const voices = await getTTSVoices(tts.provider)
          setDynamicTtsVoices(voices)

          // 如果当前没有选中音色且有可用音色，选择第一个
          if (!selectedVoice && voices.length > 0) {
            setSelectedVoice(voices[0].id)
          }
        } catch (error) {
          console.error('加载音色列表失败:', error)
          setDynamicTtsVoices([])
        }
      } else {
        setDynamicTtsVoices([])
      }
    }

    loadDynamicVoices()
  }, [tts.provider, selectedVoice])

  useEffect(() => {
    const safeAccounts = Array.isArray(accounts) ? accounts : []
    if (safeAccounts.length > 0 && accountConfigs.length === 0) {
      setAccountConfigs([{ account_id: safeAccounts[0].id, video_count: 1 }])
    }
  }, [accounts, accountConfigs.length])

  // Handle account configuration
  const handleAccountConfigChange = (index: number, field: keyof AccountConfig, value: any) => {
    setAccountConfigs(prev => {
      const newConfigs = [...prev]
      newConfigs[index] = { ...newConfigs[index], [field]: value }
      return newConfigs
    })
  }

  const addAccountConfig = () => {
    const safeAccounts = Array.isArray(accounts) ? accounts : []
    if (safeAccounts.length > 0) {
      setAccountConfigs(prev => [...prev, { account_id: safeAccounts[0].id, video_count: 1 }])
    }
  }

  const removeAccountConfig = (index: number) => {
    setAccountConfigs(prev => prev.filter((_, i) => i !== index))
  }

  // Handle material selection
  const toggleMaterialSelection = (materialId: string) => {
    setSelectedMaterials(prev => 
      prev.includes(materialId)
        ? prev.filter(id => id !== materialId)
        : [...prev, materialId]
    )
  }

  // Validation
  const isFormValid = () => {
    return (
      jobName.trim() &&
      selectedPromptCategory &&
      selectedPrompt &&
      selectedVoice &&
      selectedTemplate &&
      accountConfigs.length > 0 &&
      accountConfigs.every(config => config.account_id && config.video_count > 0) &&
      (materialSelectionMode === 'random' || selectedMaterials.length > 0) &&
      (musicSelectionMode === 'random' || selectedMusic)
    )
  }

  // Submit form
  const handleSubmit = async () => {
    if (!isFormValid()) {
      alert('请完成所有必填配置项')
      return
    }

    setIsSubmitting(true)
    try {
      const jobData: CreateVideoGenerationJobRequest = {
        name: jobName,
        description: jobDescription,
        config: {
          material_selection: materialSelectionMode,
          video_material_group: selectedMaterialCategory,
          selected_materials: materialSelectionMode === 'manual' ? selectedMaterials : undefined,
          prompt_group: selectedPromptCategory,
          prompt_id: selectedPrompt,
          voice_settings: {
            voice: selectedVoice,
            speed: speechRate,
          },
          audio_settings: {
            speech_volume: speechVolume / 100, // 转换为0-1范围
            background_music_volume: backgroundMusicVolume / 100, // 转换为0-1范围
            enable_background_music: enableBackgroundMusic,
          },
          music_selection: musicSelectionMode,
          background_music_group: selectedMusicCategory,
          music_id: musicSelectionMode === 'specific' ? selectedMusic : undefined,
          cover_template_id: selectedTemplate,
          cover_settings: {
            position: coverPosition,
            animation: coverAnimation,
            custom_x: coverPosition === 'custom' ? customCoverX : undefined,
            custom_y: coverPosition === 'custom' ? customCoverY : undefined,
            animation_duration: animationDuration
          },
          subtitle_config: {
            font_family: subtitleFont,
            font_size: subtitleSize,
            font_color: subtitleColor,
            position: subtitlePosition,
            words_per_screen: wordsPerScreen,
            stroke_thickness: strokeThickness,
            stroke_color: strokeColor,
            enabled: true,
            include_all_words: true
          },
          video_config: {
            resolution: videoResolution,
            fps: videoFps,
            output_format: 'mp4'
          },
          transition_settings: {
            enabled: enableTransitions,
            transition_type: transitionType,
            duration: transitionDuration
          }
        },
        account_configs: accountConfigs.map(config => ({
          ...config,
          account_id: String(config.account_id || (safeAccounts.length > 0 ? safeAccounts[0].id : '')) // Ensure account_id is a string and not empty
        }))
      }

      console.log('--- 提交视频生成作业 ---');
      console.log('作业名称:', jobName);
      console.log('作业描述:', jobDescription || '无');
      console.log('--- 配置详情 ---');
      console.log('素材选择模式:', materialSelectionMode);
      console.log('素材分类:', selectedMaterialCategory || '未选择');
      if (materialSelectionMode === 'manual') {
        console.log('手动选择素材:', selectedMaterials.map(id => safeMaterials.find(m => m.id === id)?.name || id));
      }
      console.log('提示词分类:', selectedPromptCategory || '未选择');
      console.log('提示词:', safePrompts.find(p => p.id === selectedPrompt)?.name || selectedPrompt || '未选择');
      console.log('音色:', safeTtsVoices.find(v => v.id === selectedVoice)?.name || selectedVoice || '未选择');
      console.log('语速:', speechRate);
      console.log('音乐选择模式:', musicSelectionMode);
      console.log('音乐分类:', selectedMusicCategory || '未选择');
      if (musicSelectionMode === 'specific') {
        console.log('固定音乐:', safeMusicList.find(m => m.id === selectedMusic)?.name || selectedMusic || '未选择');
      }
      console.log('封面模板:', safeTemplates.find(t => t.id === selectedTemplate)?.name || selectedTemplate || '未选择');
      console.log('字幕配置:', jobData.config.subtitle_config);
      console.log('视频配置:', jobData.config.video_config);
      console.log('--- 账号配置 ---');
      accountConfigs.forEach((acc, index) => {
        const accountName = safeAccounts.find(a => a.id === acc.account_id)?.name || acc.account_id;
        console.log(`账号 ${index + 1}: ${accountName}, 视频数量: ${acc.video_count}`);
      });
      console.log('--- 完整提交数据 (jobData) ---');
      console.log(JSON.stringify(jobData, null, 2));
      console.log('-----------------------------');

      const response = await apiService.videoGeneration.createJob(jobData)
      if (response.error) {
        alert(`创建作业失败: ${response.error}`)
      } else {
        alert('视频生成作业创建成功！')
        router.push('/tasks')
      }
    } catch (error) {
      alert(`提交失败: ${error}`)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Check if still loading
  const isLoading = Object.values(loading).some(Boolean)

  // 防御性检查：确保关键数据都是数组类型
  const safeMaterialCategories = Array.isArray(materialCategories) ? materialCategories : []
  const safeMusicCategories = Array.isArray(musicCategories) ? musicCategories : []
  const safePromptCategories = Array.isArray(promptCategories) ? promptCategories : []
  const safeTemplates = Array.isArray(templates) ? templates : []
  const safeTtsVoices = Array.isArray(dynamicTtsVoices) && dynamicTtsVoices.length > 0
    ? dynamicTtsVoices
    : Array.isArray(ttsVoices) ? ttsVoices : []
  const safeAccounts = Array.isArray(accounts) ? accounts : []
  const safeMaterials = Array.isArray(materials) ? materials : []
  const safeMusicList = Array.isArray(musicList) ? musicList : []
  const safePrompts = Array.isArray(prompts) ? prompts : []

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <ArrowPathIcon className="mx-auto h-12 w-12 text-blue-500 animate-spin" />
          <p className="mt-4 text-lg text-gray-600">加载配置数据中...</p>
        </div>
      </div>
    )
  }

  // Show errors if any critical data failed to load
  const criticalErrors = Object.entries(errors).filter(([_, error]) => error)
  if (criticalErrors.length > 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md mx-auto text-center">
          <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-red-500" />
          <h2 className="mt-4 text-lg font-semibold text-gray-900">数据加载失败</h2>
          <div className="mt-4 space-y-2">
            {criticalErrors.map(([key, error]) => (
              <p key={key} className="text-sm text-red-600">
                {key}: {error}
              </p>
            ))}
          </div>
          <button
            onClick={loadAllData}
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
          >
            重新加载
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto py-8 px-4">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">视频生成配置</h1>
          <p className="text-gray-600">配置批量视频生成任务的各项参数</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 左侧配置区域 */}
          <div className="lg:col-span-2 space-y-6">
            
            {/* 基础信息 */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center mb-4">
                <CogIcon className="h-5 w-5 text-blue-500 mr-2" />
                <h2 className="text-lg font-semibold text-gray-900">基础信息</h2>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    作业名称 *
                  </label>
                  <input
                    type="text"
                    value={jobName}
                    onChange={(e) => setJobName(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="输入作业名称"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    作业描述
                  </label>
                  <textarea
                    value={jobDescription}
                    onChange={(e) => setJobDescription(e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="输入作业描述（可选）"
                  />
                </div>
              </div>
            </div>

            {/* 视频素材配置 */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center mb-4">
                <RectangleStackIcon className="h-5 w-5 text-purple-500 mr-2" />
                <h2 className="text-lg font-semibold text-gray-900">视频素材配置</h2>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    素材分组 *
                  </label>
                  <select
                    value={selectedMaterialCategory}
                    onChange={(e) => setSelectedMaterialCategory(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">选择素材分组</option>
                    {safeMaterialCategories.map(category => (
                      <option key={category.id} value={category.name}>
                        {category.name} ({category.material_count}个素材)
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    选择方式 *
                  </label>
                  <div className="flex space-x-4">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        value="random"
                        checked={materialSelectionMode === 'random'}
                        onChange={(e) => setMaterialSelectionMode(e.target.value as 'random' | 'manual')}
                        className="mr-2"
                      />
                      随机选择
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        value="manual"
                        checked={materialSelectionMode === 'manual'}
                        onChange={(e) => setMaterialSelectionMode(e.target.value as 'random' | 'manual')}
                        className="mr-2"
                      />
                      手动选择
                    </label>
                  </div>
                </div>
                
                {materialSelectionMode === 'manual' && safeMaterials.length > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      选择具体素材 ({selectedMaterials.length} 已选择)
                    </label>
                    <div className="max-h-48 overflow-y-auto border border-gray-200 rounded-lg p-3">
                      <div className="grid grid-cols-1 gap-2">
                        {safeMaterials.map(material => (
                          <label key={material.id} className="flex items-center p-2 hover:bg-gray-50 rounded">
                            <input
                              type="checkbox"
                              checked={selectedMaterials.includes(material.id)}
                              onChange={() => toggleMaterialSelection(material.id)}
                              className="mr-3"
                            />
                            <div className="flex-1">
                              <div className="text-sm font-medium text-gray-900">{material.name}</div>
                              <div className="text-xs text-gray-500">
                                {material.duration}s | {material.resolution}
                              </div>
                            </div>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* 故事内容配置 */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center mb-4">
                <ChatBubbleBottomCenterTextIcon className="h-5 w-5 text-green-500 mr-2" />
                <h2 className="text-lg font-semibold text-gray-900">故事内容配置</h2>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    提示词分组 *
                  </label>
                  <select
                    value={selectedPromptCategory}
                    onChange={(e) => setSelectedPromptCategory(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">选择提示词分组</option>
                    {safePromptCategories.map(category => (
                      <option key={category} value={category}>
                        {category}
                      </option>
                    ))}
                  </select>
                </div>
                
                {safePrompts.length > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      具体提示词 *
                    </label>
                    <select
                      value={selectedPrompt}
                      onChange={(e) => setSelectedPrompt(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">选择提示词</option>
                      {safePrompts.map(prompt => (
                        <option key={prompt.id} value={prompt.id}>
                          {prompt.name}
                        </option>
                      ))}
                    </select>
                    
                    {selectedPrompt && (
                      <div className="mt-2 p-3 bg-gray-50 rounded border text-sm text-gray-600">
                        已选择: {safePrompts.find(p => p.id === selectedPrompt)?.name}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* 音频配置 */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center mb-4">
                <SpeakerWaveIcon className="h-5 w-5 text-orange-500 mr-2" />
                <h2 className="text-lg font-semibold text-gray-900">音频配置</h2>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    音色选择 *
                  </label>
                  <select
                    value={selectedVoice}
                    onChange={(e) => setSelectedVoice(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">选择音色</option>
                    {safeTtsVoices.map(voice => (
                      <option key={voice.id} value={voice.id}>
                        {voice.name} ({voice.language} - {voice.gender})
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    语速倍率: {speechRate}x
                  </label>
                  <input
                    type="range"
                    min="0.5"
                    max="2.0"
                    step="0.1"
                    value={speechRate}
                    onChange={(e) => setSpeechRate(parseFloat(e.target.value))}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>0.5x (慢)</span>
                    <span>1.0x (正常)</span>
                    <span>2.0x (快)</span>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    语音音量: {speechVolume}%
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="100"
                    step="5"
                    value={speechVolume}
                    onChange={(e) => setSpeechVolume(parseInt(e.target.value))}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>0% (静音)</span>
                    <span>100% (最大)</span>
                  </div>
                </div>
                
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm font-medium text-gray-700">
                      背景音乐音量: {enableBackgroundMusic ? `${backgroundMusicVolume}%` : '禁用'}
                    </label>
                    <label className="inline-flex items-center">
                      <input
                        type="checkbox"
                        checked={enableBackgroundMusic}
                        onChange={(e) => setEnableBackgroundMusic(e.target.checked)}
                        className="form-checkbox h-4 w-4 text-indigo-600"
                      />
                      <span className="ml-2 text-sm text-gray-700">启用背景音乐</span>
                    </label>
                  </div>
                  {enableBackgroundMusic && (
                    <>
                      <input
                        type="range"
                        min="0"
                        max="50"
                        step="5"
                        value={backgroundMusicVolume}
                        onChange={(e) => setBackgroundMusicVolume(parseInt(e.target.value))}
                        className="w-full"
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>0% (静音)</span>
                        <span>15% (推荐)</span>
                        <span>50% (最大)</span>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* 背景音乐配置 */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center mb-4">
                <FolderIcon className="h-5 w-5 text-indigo-500 mr-2" />
                <h2 className="text-lg font-semibold text-gray-900">背景音乐配置</h2>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    音乐分组 *
                  </label>
                  <select
                    value={selectedMusicCategory}
                    onChange={(e) => setSelectedMusicCategory(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">选择音乐分组</option>
                    {safeMusicCategories.map(category => (
                      <option key={category} value={category}>
                        {category}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    选择方式 *
                  </label>
                  <div className="flex space-x-4">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        value="random"
                        checked={musicSelectionMode === 'random'}
                        onChange={(e) => setMusicSelectionMode(e.target.value as 'random' | 'specific')}
                        className="mr-2"
                      />
                      随机选择
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        value="specific"
                        checked={musicSelectionMode === 'specific'}
                        onChange={(e) => setMusicSelectionMode(e.target.value as 'random' | 'specific')}
                        className="mr-2"
                      />
                      固定音乐
                    </label>
                  </div>
                </div>
                
                {musicSelectionMode === 'specific' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      选择具体音乐 *
                    </label>
                    <select
                      value={selectedMusic}
                      onChange={(e) => setSelectedMusic(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      disabled={musicLoading || safeMusicList.length === 0}
                    >
                      <option value="">{musicLoading ? '加载中...' : (safeMusicList.length > 0 ? '选择音乐' : '该分组下无音乐')}</option>
                      {safeMusicList.map(music => (
                        <option key={music.id} value={music.id}>
                          {music.name} - {music.artist || 'Unknown'} ({Math.round(music.duration)}s)
                        </option>
                      ))}
                    </select>
                  </div>
                )}
              </div>
            </div>

            {/* 封面配置 */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center mb-4">
                <PhotoIcon className="h-5 w-5 text-pink-500 mr-2" />
                <h2 className="text-lg font-semibold text-gray-900">封面配置</h2>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    封面模板 *
                  </label>
                  <select
                    value={selectedTemplate}
                    onChange={(e) => setSelectedTemplate(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">选择封面模板</option>
                    {safeTemplates.map(template => (
                      <option key={template.id} value={template.id}>
                        {template.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      封面位置
                    </label>
                    <select
                      value={coverPosition}
                      onChange={(e) => setCoverPosition(e.target.value as 'top' | 'center' | 'bottom' | 'custom')}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="top">画面上方</option>
                      <option value="center">画面居中</option>
                      <option value="bottom">画面下方</option>
                      <option value="custom">自定义位置</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      动画效果
                    </label>
                    <select
                      value={coverAnimation}
                      onChange={(e) => setCoverAnimation(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="none">无动画</option>
                      <option value="fade_in">淡入</option>
                      <option value="fade_out">淡出</option>
                      <option value="fade_in_out">淡入淡出</option>
                      <option value="slide_in_left">从左滑入</option>
                      <option value="slide_in_right">从右滑入</option>
                      <option value="slide_in_top">从上滑入</option>
                      <option value="slide_in_bottom">从下滑入</option>
                      <option value="slide_out_left">向左滑出</option>
                      <option value="slide_out_right">向右滑出</option>
                      <option value="slide_out_top">向上滑出</option>
                      <option value="slide_out_bottom">向下滑出</option>
                      {/* <option value="zoom_in">缩放进入</option>
                      <option value="zoom_out">缩放退出</option>
                      <option value="zoom_in_out">缩放进出</option> */}
                    </select>
                  </div>
                </div>

                {coverPosition === 'custom' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        自定义X位置
                      </label>
                      <input
                        type="text"
                        value={customCoverX}
                        onChange={(e) => setCustomCoverX(e.target.value)}
                        placeholder="如：(main_w-overlay_w)/2"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        自定义Y位置
                      </label>
                      <input
                        type="text"
                        value={customCoverY}
                        onChange={(e) => setCustomCoverY(e.target.value)}
                        placeholder="如：(main_h-overlay_h)/4"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                  </div>
                )}

                {coverAnimation !== 'none' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      动画时长（秒）
                    </label>
                    <input
                      type="number"
                      value={animationDuration}
                      onChange={(e) => setAnimationDuration(parseFloat(e.target.value) || 0.5)}
                      min="0.1"
                      max="2.0"
                      step="0.1"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                )}
              </div>
            </div>

            {/* 字幕和视频配置 */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center mb-4">
                <CogIcon className="h-5 w-5 text-gray-500 mr-2" />
                <h2 className="text-lg font-semibold text-gray-900">字幕和视频配置</h2>
              </div>
              
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <label className="block text-sm font-medium text-gray-700">
                        字体
                        {fontsLoaded && !useCustomFont && (
                          <span className="text-xs text-gray-500 ml-2">
                            ({availableFonts.filter(f => showAllFonts || f.available).length}个{showAllFonts ? '' : '可用'})
                          </span>
                        )}
                      </label>
                      <div className="flex items-center space-x-2 text-xs">
                        {fontsLoaded && !useCustomFont && (
                          <label className="flex items-center text-gray-600">
                            <input
                              type="checkbox"
                              checked={showAllFonts}
                              onChange={(e) => setShowAllFonts(e.target.checked)}
                              className="mr-1"
                            />
                            显示所有字体
                          </label>
                        )}
                        <label className="flex items-center text-blue-600">
                          <input
                            type="checkbox"
                            checked={useCustomFont}
                            onChange={(e) => {
                              setUseCustomFont(e.target.checked)
                              if (e.target.checked) {
                                setSubtitleFont(customFontName || 'Arial')
                              } else {
                                setSubtitleFont('ZY Starry')
                              }
                            }}
                            className="mr-1"
                          />
                          自定义字体
                        </label>
                      </div>
                    </div>

                    {useCustomFont ? (
                      <input
                        type="text"
                        value={customFontName}
                        onChange={(e) => {
                          setCustomFontName(e.target.value)
                          setSubtitleFont(e.target.value || 'Arial')
                        }}
                        placeholder="输入字体名称，如：Arial, Microsoft YaHei, 微软雅黑"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        style={{ fontFamily: customFontName || 'Arial' }}
                      />
                    ) : (
                      <select
                        value={subtitleFont}
                        onChange={(e) => setSubtitleFont(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        style={{ fontFamily: subtitleFont }}
                      >
                        {!fontsLoaded ? (
                          <option value="">正在检测字体...</option>
                        ) : (
                          ['装饰性', '中文', '英文', '系统'].map(category => {
                            const categoryFonts = availableFonts.filter(font =>
                              font.category === category && (showAllFonts || font.available)
                            )
                            if (categoryFonts.length === 0) return null

                            return (
                              <optgroup key={category} label={`${category}字体`}>
                                {categoryFonts.map(font => (
                                  <option
                                    key={font.family}
                                    value={font.family}
                                    style={{
                                      fontFamily: font.family,
                                      color: font.available ? 'black' : '#999',
                                      fontWeight: font.available ? 'normal' : 'lighter'
                                    }}
                                    disabled={!font.available && !showAllFonts}
                                  >
                                    {font.name}{!font.available ? ' (系统未检测到)' : ''}
                                  </option>
                                ))}
                              </optgroup>
                            )
                          })
                        )}
                      </select>
                    )}
                    
                    {/* 字体预览 */}
                    {subtitleFont && (
                      <div className="mt-2 p-3 bg-gray-50 rounded border text-sm">
                        <div className="text-xs text-gray-500 mb-1">预览效果：</div>
                        <div style={{ fontFamily: subtitleFont, fontSize: '16px' }}>
                          字体预览 Font Preview 123
                        </div>
                        <div style={{ fontFamily: subtitleFont, fontSize: '14px' }} className="mt-1 text-gray-600">
                          The quick brown fox jumps over the lazy dog
                        </div>
                      </div>
                    )}
                    
                    {/* 检测提示 */}
                    {fontsLoaded && !showAllFonts && (
                      <div className="mt-2 text-xs text-blue-600">
                        💡 提示：只显示系统检测到的字体，如需查看全部字体请勾选"显示所有字体"
                      </div>
                    )}
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      字号
                    </label>
                    <input
                      type="number"
                      value={subtitleSize}
                      onChange={(e) => setSubtitleSize(parseInt(e.target.value))}
                      min="12"
                      max="72"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      颜色
                    </label>
                    <input
                      type="color"
                      value={subtitleColor}
                      onChange={(e) => setSubtitleColor(e.target.value)}
                      className="w-full h-10 border border-gray-300 rounded-lg"
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      字幕位置
                    </label>
                    <select
                      value={subtitlePosition}
                      onChange={(e) => setSubtitlePosition(e.target.value as 'top' | 'center' | 'bottom')}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="top">上方</option>
                      <option value="center">居中</option>
                      <option value="bottom">下方</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      每屏单词数
                    </label>
                    <input
                      type="number"
                      value={wordsPerScreen}
                      onChange={(e) => setWordsPerScreen(parseInt(e.target.value) || 1)}
                      min="1"
                      max="10"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      控制每屏显示的单词数量，默认1个单词
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      描边厚度
                    </label>
                    <input
                      type="number"
                      value={strokeThickness}
                      onChange={(e) => setStrokeThickness(parseInt(e.target.value) || 0)}
                      min="0"
                      max="10"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      字幕描边厚度，0表示无描边
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      描边颜色
                    </label>
                    <input
                      type="color"
                      value={strokeColor}
                      onChange={(e) => setStrokeColor(e.target.value)}
                      className="w-full h-10 border border-gray-300 rounded-lg"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      字幕描边颜色，增强文字可读性
                    </p>
                  </div>
                </div>

                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="text-sm text-gray-600">
                    <p className="font-medium mb-2">💡 字幕配置说明：</p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs">
                      <div>
                        <p>• <strong>每屏单词数</strong>：控制字幕显示密度</p>
                        <p>• <strong>智能断句</strong>：优先按句子结构显示</p>
                        <p>• <strong>标点处理</strong>：单词后连续标点一起显示</p>
                      </div>
                      <div>
                        <p>• <strong>描边效果</strong>：提升文字在复杂背景下的可读性</p>
                        <p>• <strong>字体检测</strong>：自动检测系统可用字体</p>
                        {fontsLoaded && (
                          <p className="text-green-600 font-medium mt-1">
                            ✓ 检测到 {availableFonts.filter(f => f.available).length} 个可用字体
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      视频分辨率
                    </label>
                    <select
                      value={videoResolution}
                      onChange={(e) => setVideoResolution(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="1080x1920">1080x1920 (竖屏)</option>
                      <option value="1920x1080">1920x1080 (横屏)</option>
                      <option value="720x1280">720x1280 (竖屏)</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      帧率
                    </label>
                    <select
                      value={videoFps}
                      onChange={(e) => setVideoFps(parseInt(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value={24}>24 fps</option>
                      <option value={30}>30 fps</option>
                      <option value={60}>60 fps</option>
                    </select>
                  </div>
                </div>

                {/* 转场效果配置 */}
                <div className="border-t pt-4">
                  <div className="flex items-center mb-3">
                    <label className="flex items-center text-sm font-medium text-gray-700">
                      <input
                        type="checkbox"
                        checked={enableTransitions}
                        onChange={(e) => setEnableTransitions(e.target.checked)}
                        className="mr-2"
                      />
                      启用视频素材转场效果
                    </label>
                  </div>

                  {enableTransitions && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          转场类型
                        </label>
                        <select
                          value={transitionType}
                          onChange={(e) => setTransitionType(e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                          <option value="fade">淡入淡出</option>
                          <option value="dissolve">溶解过渡</option>
                          <option value="slide_left">向左滑动</option>
                          <option value="slide_right">向右滑动</option>
                          <option value="slide_up">向上滑动</option>
                          <option value="slide_down">向下滑动</option>
                          <option value="wipe_left">向左擦除</option>
                          <option value="wipe_right">向右擦除</option>
                          <option value="wipe_up">向上擦除</option>
                          <option value="wipe_down">向下擦除</option>
                          <option value="circle_open">圆形展开</option>
                          <option value="circle_close">圆形收缩</option>
                          <option value="radial">径向过渡</option>
                          <option value="smooth_left">平滑左移</option>
                          <option value="smooth_right">平滑右移</option>
                          <option value="smooth_up">平滑上移</option>
                          <option value="smooth_down">平滑下移</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          转场时长（秒）
                        </label>
                        <input
                          type="number"
                          value={transitionDuration}
                          onChange={(e) => setTransitionDuration(parseFloat(e.target.value) || 0.5)}
                          min="0.1"
                          max="2.0"
                          step="0.1"
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                        <p className="text-xs text-gray-500 mt-1">
                          建议0.5秒，过长会影响观看体验
                        </p>
                      </div>
                    </div>
                  )}

                  <div className="bg-blue-50 rounded-lg p-3 mt-3">
                    <p className="text-sm text-blue-700">
                      💡 转场效果会在视频素材之间添加平滑过渡，让视频更加流畅自然
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* 账号配置 */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <UserGroupIcon className="h-5 w-5 text-blue-500 mr-2" />
                  <h2 className="text-lg font-semibold text-gray-900">账号配置</h2>
                </div>
                <button
                  onClick={addAccountConfig}
                  className="flex items-center px-3 py-1 text-sm bg-blue-500 text-white rounded-lg hover:bg-blue-600"
                >
                  <PlusIcon className="h-4 w-4 mr-1" />
                  添加账号
                </button>
              </div>
              
              <div className="space-y-3">
                {accountConfigs.map((config, index) => (
                  <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <div className="flex-1">
                      <select
                        value={config.account_id}
                        onChange={(e) => handleAccountConfigChange(index, 'account_id', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="">选择账号</option>
                        {safeAccounts.map(account => (
                          <option key={account.id} value={account.id}>
                            {account.name} ({account.platform})
                          </option>
                        ))}
                      </select>
                    </div>
                    
                    <div className="w-24">
                      <input
                        type="number"
                        value={config.video_count}
                        onChange={(e) => handleAccountConfigChange(index, 'video_count', parseInt(e.target.value))}
                        min="1"
                        max="100"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-center"
                        placeholder="数量"
                      />
                    </div>
                    
                    {accountConfigs.length > 1 && (
                      <button
                        onClick={() => removeAccountConfig(index)}
                        className="p-2 text-red-500 hover:bg-red-50 rounded-lg"
                      >
                        <XMarkIcon className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                ))}
              </div>
              
              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <p className="text-sm text-blue-700">
                  总任务数: {accountConfigs.reduce((sum, config) => sum + (config.video_count || 0), 0)} 个视频
                </p>
              </div>
            </div>
          </div>

          {/* 右侧预览和操作区域 */}
          <div className="space-y-6">
            {/* 配置预览 */}
            <div className="bg-white rounded-lg shadow-sm p-6 sticky top-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">配置预览</h3>
                <button
                  onClick={() => setShowPreview(!showPreview)}
                  className="flex items-center text-sm text-blue-500 hover:text-blue-600"
                >
                  <EyeIcon className="h-4 w-4 mr-1" />
                  {showPreview ? '隐藏' : '显示'}
                </button>
              </div>
              
              {showPreview && (
                <div className="space-y-3 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">作业名称:</span>
                    <p className="text-gray-600">{jobName || '未设置'}</p>
                  </div>
                  
                  <div>
                    <span className="font-medium text-gray-700">素材配置:</span>
                    <p className="text-gray-600">
                      {selectedMaterialCategory || '未选择'} - {materialSelectionMode === 'random' ? '随机选择' : `手动选择 ${selectedMaterials.length} 个`}
                    </p>
                  </div>
                  
                  <div>
                    <span className="font-medium text-gray-700">提示词:</span>
                    <p className="text-gray-600">
                      {selectedPromptCategory || '未选择'} - {safePrompts.find(p => p.id === selectedPrompt)?.name || '未选择'}
                    </p>
                  </div>
                  
                  <div>
                    <span className="font-medium text-gray-700">音色:</span>
                    <p className="text-gray-600">
                      {safeTtsVoices.find(v => v.id === selectedVoice)?.name || '未选择'} ({speechRate}x)
                    </p>
                  </div>
                  
                  <div>
                    <span className="font-medium text-gray-700">背景音乐:</span>
                    <p className="text-gray-600">
                      {selectedMusicCategory || '未选择'} - {musicSelectionMode === 'random' ? '随机选择' : safeMusicList.find(m => m.id === selectedMusic)?.name || '未选择'}
                    </p>
                  </div>
                  
                  <div>
                    <span className="font-medium text-gray-700">总任务数:</span>
                    <p className="text-gray-600">
                      {accountConfigs.reduce((sum, config) => sum + (config.video_count || 0), 0)} 个视频
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* 提交按钮 */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <button
                onClick={handleSubmit}
                disabled={!isFormValid() || isSubmitting}
                className={`w-full flex items-center justify-center px-4 py-3 rounded-lg font-medium ${
                  isFormValid() && !isSubmitting
                    ? 'bg-blue-500 text-white hover:bg-blue-600'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                {isSubmitting ? (
                  <>
                    <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                    创建作业中...
                  </>
                ) : (
                  <>
                    <PlayIcon className="h-4 w-4 mr-2" />
                    创建视频生成作业
                  </>
                )}
              </button>
              
              {!isFormValid() && (
                <p className="mt-2 text-sm text-red-600 text-center">
                  请完成所有必填配置项
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
