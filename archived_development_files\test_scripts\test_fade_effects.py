#!/usr/bin/env python3
"""
测试封面淡入淡出效果 - 验证动画是否正常工作
"""

import sys
import os
import logging
from pathlib import Path
import ffmpeg
import tempfile

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.video_generation_helpers import VideoCompositionService

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_fade_effects():
    """测试各种淡入淡出效果"""
    
    # 测试文件路径
    video_path = "uploads/videos/RedditTour_07030014_视频1.mp4"
    cover_path = "uploads/covers/1_02c58438-97a3-435f-8ed0-5b669bc167ed_cover.png"
    
    # 检查文件是否存在
    if not Path(video_path).exists():
        logger.error(f"测试视频文件不存在: {video_path}")
        return False
        
    if not Path(cover_path).exists():
        logger.error(f"测试封面文件不存在: {cover_path}")
        return False
    
    logger.info("🔄 开始测试封面淡入淡出效果...")
    
    # 测试配置
    test_configs = [
        {
            'name': '淡入效果',
            'animation': 'fade_in',
            'duration': 5.0,
            'animation_duration': 1.0,
            'output': 'test_fade_in.mp4'
        },
        {
            'name': '淡出效果', 
            'animation': 'fade_out',
            'duration': 5.0,
            'animation_duration': 1.0,
            'output': 'test_fade_out.mp4'
        },
        {
            'name': '淡入淡出效果',
            'animation': 'fade_in_out',
            'duration': 6.0,
            'animation_duration': 1.0,
            'output': 'test_fade_in_out.mp4'
        },
        {
            'name': '无动画效果',
            'animation': 'none',
            'duration': 4.0,
            'animation_duration': 0.0,
            'output': 'test_no_animation.mp4'
        }
    ]
    
    try:
        # 获取视频信息
        probe = ffmpeg.probe(video_path)
        video_info = next(s for s in probe['streams'] if s['codec_type'] == 'video')
        width = int(video_info['width'])
        height = int(video_info['height'])
        
        logger.info(f"视频信息: {width}x{height}")
        
        # 计算封面参数
        cover_width = int(width * 0.8)
        corner_radius = int(cover_width * 0.06)
        
        logger.info(f"封面配置: 宽度={cover_width}px, 圆角={corner_radius}px")
        
        success_count = 0
        
        for i, config in enumerate(test_configs):
            logger.info(f"\n--- 测试 {i+1}: {config['name']} ---")
            logger.info(f"动画类型: {config['animation']}")
            logger.info(f"显示时长: {config['duration']}s")
            logger.info(f"动画时长: {config['animation_duration']}s")
            logger.info(f"输出文件: {config['output']}")
            
            try:
                # 创建临时目录
                with tempfile.TemporaryDirectory() as temp_dir:
                    temp_path = Path(temp_dir)
                    
                    # 创建圆角封面
                    rounded_cover_path = temp_path / f"rounded_cover_{i}.png"
                    
                    success = VideoCompositionService._create_rounded_cover_image(
                        cover_path, cover_width, corner_radius, str(rounded_cover_path)
                    )
                    
                    if not success or not rounded_cover_path.exists():
                        logger.error(f"❌ 圆角封面创建失败: {config['name']}")
                        continue
                    
                    # 创建视频流
                    video_stream = ffmpeg.input(video_path)
                    cover_overlay = ffmpeg.input(str(rounded_cover_path))
                    
                    # 封面设置
                    cover_settings = {
                        'position': 'center',
                        'animation': config['animation'],
                        'animation_duration': config['animation_duration']
                    }
                    
                    # 应用封面叠加效果
                    final_video = VideoCompositionService._apply_cover_overlay(
                        video_stream, cover_overlay, config['duration'], cover_settings
                    )
                    
                    # 输出视频
                    out = ffmpeg.output(
                        final_video,
                        config['output'],
                        vcodec='libx264',
                        preset='fast',
                        pix_fmt='yuv420p',
                        t=8  # 生成8秒测试视频，确保能看到完整效果
                    ).overwrite_output()
                    
                    logger.info("开始执行FFmpeg命令...")
                    
                    # 显示FFmpeg命令（简化版）
                    cmd = ffmpeg.compile(out)
                    logger.info(f"FFmpeg命令长度: {len(' '.join(cmd))} 字符")
                    
                    # 执行
                    ffmpeg.run(out, quiet=True)  # 静默执行，减少输出
                    
                    # 检查结果
                    if Path(config['output']).exists():
                        file_size = Path(config['output']).stat().st_size
                        logger.info(f"✅ {config['name']} 测试成功! 文件大小: {file_size} bytes")
                        success_count += 1
                    else:
                        logger.error(f"❌ {config['name']} 测试失败: 输出文件不存在")
                        
            except Exception as e:
                logger.error(f"❌ {config['name']} 测试过程中发生错误: {e}")
        
        logger.info(f"\n=== 测试完成 ===")
        logger.info(f"成功: {success_count}/{len(test_configs)} 个测试")
        
        if success_count == len(test_configs):
            logger.info("🎉 所有淡入淡出效果测试通过!")
            return True
        else:
            logger.warning(f"⚠️ 部分测试失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        return False

def create_comparison_video():
    """创建一个对比视频，展示所有效果"""
    
    logger.info("\n🔄 创建效果对比视频...")
    
    test_files = [
        'test_fade_in.mp4',
        'test_fade_out.mp4', 
        'test_fade_in_out.mp4',
        'test_no_animation.mp4'
    ]
    
    # 检查所有测试文件是否存在
    missing_files = [f for f in test_files if not Path(f).exists()]
    if missing_files:
        logger.error(f"缺少测试文件: {missing_files}")
        return False
    
    try:
        # 创建并排对比视频
        inputs = [ffmpeg.input(f) for f in test_files]
        
        # 缩放每个视频到1/4大小
        scaled = [inp.filter('scale', 540, 960) for inp in inputs]
        
        # 创建2x2网格布局
        top_row = ffmpeg.filter([scaled[0], scaled[1]], 'hstack')
        bottom_row = ffmpeg.filter([scaled[2], scaled[3]], 'hstack')
        final = ffmpeg.filter([top_row, bottom_row], 'vstack')
        
        # 添加文字标签
        labeled = final.filter('drawtext', 
                              text='淡入(左上) | 淡出(右上) | 淡入淡出(左下) | 无动画(右下)',
                              fontsize=24,
                              fontcolor='white',
                              x=10,
                              y=10)
        
        # 输出对比视频
        out = ffmpeg.output(
            labeled,
            'comparison_fade_effects.mp4',
            vcodec='libx264',
            preset='fast',
            pix_fmt='yuv420p',
            t=8
        ).overwrite_output()
        
        logger.info("开始创建对比视频...")
        ffmpeg.run(out, quiet=True)
        
        if Path('comparison_fade_effects.mp4').exists():
            file_size = Path('comparison_fade_effects.mp4').stat().st_size
            logger.info(f"✅ 对比视频创建成功! 文件: comparison_fade_effects.mp4, 大小: {file_size} bytes")
            return True
        else:
            logger.error("❌ 对比视频创建失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 创建对比视频失败: {e}")
        return False

if __name__ == "__main__":
    logger.info("🚀 开始淡入淡出效果测试")
    
    # 测试1: 各种淡入淡出效果
    success1 = test_fade_effects()
    
    # 测试2: 创建对比视频
    success2 = create_comparison_video()
    
    if success1:
        logger.info("\n📋 测试结果文件:")
        logger.info("- test_fade_in.mp4 (淡入效果)")
        logger.info("- test_fade_out.mp4 (淡出效果)")
        logger.info("- test_fade_in_out.mp4 (淡入淡出效果)")
        logger.info("- test_no_animation.mp4 (无动画效果)")
        
        if success2:
            logger.info("- comparison_fade_effects.mp4 (效果对比视频)")
        
        logger.info("\n🎬 如何测试:")
        logger.info("1. 播放各个测试文件，观察封面的淡入淡出效果")
        logger.info("2. test_fade_in.mp4: 封面应该从透明逐渐变为不透明")
        logger.info("3. test_fade_out.mp4: 封面应该从不透明逐渐变为透明")
        logger.info("4. test_fade_in_out.mp4: 封面应该淡入后再淡出")
        logger.info("5. test_no_animation.mp4: 封面应该直接显示，无动画")
        logger.info("6. comparison_fade_effects.mp4: 四种效果的并排对比")
        
        logger.info("\n🎉 所有测试文件生成完成!")
    else:
        logger.error("\n❌ 测试失败")
        sys.exit(1)
