#!/bin/bash

echo "开始测试背景音乐页面修复..."

# 启动后端
echo "启动后端服务器..."
cd "d:/SHAOJIAHAO/IDE_WORKSPACES/CursorAICoding/RedditStoryVideoGenerator/backend"
python src/main.py &
BACKEND_PID=$!

# 等待后端启动
sleep 5

# 启动前端
echo "启动前端开发服务器..."
cd "../frontend" 
npm run dev &
FRONTEND_PID=$!

echo "服务器已启动，请访问 http://localhost:3000/music 测试页面"
echo "后端PID: $BACKEND_PID"
echo "前端PID: $FRONTEND_PID"

# 等待用户输入后清理
read -p "测试完成后按回车键停止服务器..."

# 清理进程
kill $BACKEND_PID $FRONTEND_PID
echo "服务器已停止"
