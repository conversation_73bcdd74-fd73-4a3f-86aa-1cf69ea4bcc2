#!/usr/bin/env python3
"""
测试两段视频转场 - 验证修复后的转场时间计算
"""

import sys
import os
import logging
from pathlib import Path
import ffmpeg
import tempfile
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.video_generation_helpers import VideoCompositionService

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_test_segments():
    """创建两个测试视频片段"""
    
    test_videos = []
    
    # 创建两个不同时长的视频片段
    video_configs = [
        ('red', '#FF0000', 4.0),    # 4秒红色
        ('blue', '#0000FF', 6.0),   # 6秒蓝色
    ]
    
    durations = []
    
    for i, (name, color, duration) in enumerate(video_configs):
        output_path = f"test_two_{name}_{duration}s.mp4"
        
        if not Path(output_path).exists():
            logger.info(f"创建{duration}秒测试视频: {output_path}")
            
            # 创建指定时长的纯色视频
            (
                ffmpeg
                .input(f'color={color}:size=1080x1920:duration={duration}:rate=30', f='lavfi')
                .output(output_path, vcodec='libx264', pix_fmt='yuv420p')
                .overwrite_output()
                .run(quiet=True)
            )
        
        test_videos.append(output_path)
        durations.append(duration)
    
    return test_videos, durations

def test_two_segment_transitions():
    """测试两段视频的转场效果"""
    
    logger.info("🎬 开始测试两段视频转场效果...")
    
    # 创建测试视频
    test_videos, real_durations = create_test_segments()
    
    logger.info(f"测试视频片段: {test_videos}")
    logger.info(f"实际时长: {real_durations}")
    
    # 测试配置
    test_configs = [
        {
            'name': '两段淡入淡出转场',
            'transition_type': 'fade',
            'duration': 1.0,
            'output': 'two_segment_fade.mp4'
        },
        {
            'name': '两段溶解转场',
            'transition_type': 'dissolve',
            'duration': 1.5,
            'output': 'two_segment_dissolve.mp4'
        },
        {
            'name': '两段无转场对比',
            'transition_type': 'none',
            'duration': 0.0,
            'output': 'two_segment_none.mp4'
        }
    ]
    
    success_count = 0
    
    for i, config in enumerate(test_configs):
        logger.info(f"\n--- 测试 {i+1}: {config['name']} ---")
        logger.info(f"转场类型: {config['transition_type']}")
        logger.info(f"转场时长: {config['duration']}s")
        logger.info(f"视频片段时长: {real_durations}")
        logger.info(f"输出文件: {config['output']}")
        
        try:
            start_time = time.time()
            
            # 创建视频流
            streams = []
            for video_path in test_videos:
                stream = ffmpeg.input(video_path)
                streams.append(stream)
            
            logger.info(f"创建了 {len(streams)} 个视频流")
            
            # 应用转场效果
            final_stream = VideoCompositionService._create_video_with_transitions(
                streams, config['transition_type'], config['duration'], real_durations
            )
            
            # 输出视频
            out = ffmpeg.output(
                final_stream,
                config['output'],
                vcodec='libx264',
                preset='fast',
                pix_fmt='yuv420p'
            ).overwrite_output()
            
            # 执行FFmpeg命令
            import subprocess
            
            cmd = ffmpeg.compile(out)
            logger.info(f"FFmpeg命令长度: {len(' '.join(cmd))} 字符")
            
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            try:
                # 等待最多60秒
                stdout, stderr = process.communicate(timeout=60)
                
                if process.returncode == 0:
                    end_time = time.time()
                    processing_time = end_time - start_time
                    
                    # 检查结果
                    if Path(config['output']).exists():
                        file_size = Path(config['output']).stat().st_size
                        logger.info(f"✅ {config['name']} 测试成功!")
                        logger.info(f"   文件大小: {file_size} bytes")
                        logger.info(f"   处理时间: {processing_time:.2f}秒")
                        
                        # 计算预期的总时长
                        if config['transition_type'] == 'none':
                            expected_duration = sum(real_durations)
                            logger.info(f"   预期总时长: {expected_duration}s (无转场)")
                        else:
                            expected_duration = sum(real_durations) - config['duration']
                            logger.info(f"   预期总时长: {expected_duration}s (有{config['duration']}s转场)")
                        
                        logger.info(f"   预期效果: 红色({real_durations[0]}s) → 蓝色({real_durations[1]}s)")
                        
                        success_count += 1
                    else:
                        logger.error(f"❌ {config['name']} 测试失败: 输出文件不存在")
                else:
                    logger.error(f"❌ {config['name']} FFmpeg执行失败，返回码: {process.returncode}")
                    if stderr:
                        stderr_text = stderr.decode('utf-8', errors='ignore')
                        logger.error(f"stderr: {stderr_text[:500]}...")
                        
            except subprocess.TimeoutExpired:
                logger.error(f"❌ {config['name']} 测试超时（60秒）")
                process.kill()
                process.communicate()
                
        except Exception as e:
            logger.error(f"❌ {config['name']} 测试过程中发生错误: {e}")
            import traceback
            logger.error(traceback.format_exc())
    
    logger.info(f"\n=== 两段转场测试完成 ===")
    logger.info(f"成功: {success_count}/{len(test_configs)} 个测试")
    
    if success_count > 0:
        logger.info("\n📋 两段转场测试文件:")
        logger.info("- two_segment_fade.mp4 (淡入淡出转场)")
        logger.info("- two_segment_dissolve.mp4 (溶解转场)")
        logger.info("- two_segment_none.mp4 (无转场对比)")
        
        logger.info("\n🔍 验证要点:")
        logger.info("1. 视频应该完整播放，不会停在某个帧")
        logger.info("2. 红色片段应该完整播放4秒")
        logger.info("3. 转场应该在红色片段结束前开始")
        logger.info("4. 蓝色片段应该完整播放剩余时间")
        logger.info("5. 总时长应该符合预期")
        
        logger.info("\n🎬 预期播放效果:")
        logger.info("- fade: 红色(4s) → [1s淡入淡出] → 蓝色(剩余5s) = 总计9s")
        logger.info("- dissolve: 红色(4s) → [1.5s溶解] → 蓝色(剩余4.5s) = 总计8.5s")
        logger.info("- none: 红色(4s) → 蓝色(6s) = 总计10s")
        
        logger.info("\n如果播放效果符合预期，说明两段转场修复成功！")
        
        return True
    else:
        logger.error("❌ 所有两段转场测试都失败了")
        return False

def cleanup_test_files():
    """清理测试文件"""
    test_files = [
        'test_two_red_4.0s.mp4',
        'test_two_blue_6.0s.mp4'
    ]
    
    for file_path in test_files:
        if Path(file_path).exists():
            Path(file_path).unlink()
            logger.info(f"清理测试文件: {file_path}")

if __name__ == "__main__":
    logger.info("🚀 开始两段视频转场测试")
    logger.info("验证修复后的转场时间计算")
    
    try:
        success = test_two_segment_transitions()
        
        if success:
            logger.info("\n🎉 两段转场测试完成!")
            logger.info("转场时间计算修复成功！")
        else:
            logger.error("\n❌ 两段转场测试失败")
            sys.exit(1)
            
    finally:
        # 清理测试文件
        cleanup_test_files()
