import asyncio
import os
import random
import sys
import traceback
from pathlib import Path
from dotenv import load_dotenv

from sqlalchemy import create_engine, func
from sqlalchemy.orm import sessionmaker

# --- 设置环境 ---
# 将 'backend' 目录添加到 Python 路径中
backend_path = Path(__file__).parent / 'backend'
sys.path.insert(0, str(backend_path))

# 加载 .env 文件
load_dotenv(dotenv_path=backend_path / '.env')

from backend.src.models import Account, VideoMaterial, BackgroundMusic, Prompt, CoverTemplate, VideoGenerationTask, VideoGenerationJob, TaskStatus
from backend.src.services.video_generation_service import VideoGenerationService
from loguru import logger

# --- 日志配置 ---
logger.remove()
logger.add(sys.stderr, level="DEBUG")

# --- 数据库连接 ---
DATABASE_URL = f"sqlite:///{backend_path / 'reddit_story_generator.db'}"
engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

async def run_demo():
    """
    运行一个完整的视频生成演示。
    """
    session_maker = SessionLocal
    db = session_maker()
    try:
        print("--- 开始视频生成演示 ---")

        # --- 1. 从数据库随机选择资源 ---
        print("1. 正在从数据库中选择资源...")
        
        account = db.query(Account).order_by(func.random()).first()
        if not account:
            print("错误：数据库中没有找到账号。请添加一些数据。")
            return

        # video_material_group = db.query(VideoMaterial.category).distinct().first()
        # if not video_material_group:
        #     print("错误：数据库中没有找到视频素材分组。")
        #     return
        # video_material_group = video_material_group[0]
        video_material_group = "shorts"

        prompt = db.query(Prompt).order_by(func.random()).first()
        if not prompt:
            print("错误：数据库中没有找到提示词。")
            return

        # music_group = db.query(BackgroundMusic.category).distinct().first()
        # if not music_group:
        #     print("错误：数据库中没有找到背景音乐分组。")
        #     return
        # music_group = music_group[0]
        music_group = "纯音乐"

        cover_template = db.query(CoverTemplate).order_by(func.random()).first()
        if not cover_template:
            print("错误：数据库中没有找到封面模板。")
            return

        print(f"   - 选定账号: {account.name}")
        print(f"   - 选定视频素材分组: {video_material_group}")
        print(f"   - 选定提示词: {prompt.name}")
        print(f"   - 选定音乐分组: {music_group}")
        print(f"   - 选定封面模板: {cover_template.name}")

        # --- 2. 准备任务和配置 ---
        print("\n2. 正在准备生成任务...")
        
        job = VideoGenerationJob(name="Demo Job")
        db.add(job)
        db.commit()
        db.refresh(job)

        video_gen_service = VideoGenerationService(session_maker)
        
        task = VideoGenerationTask(
            job_id=job.id,
            task_name="Demo Task",
            account_id=account.id,
            status=TaskStatus.PENDING
        )
        db.add(task)
        db.commit()

        job_config = {
            "video_material_group": video_material_group,
            "material_selection": "random",
            "prompt_id": prompt.id,
            "voice_settings": {"voice": "zh_male_beijingxiaoye_emo_v2_mars_bigtts", "speed": 1.2},
            "background_music_group": music_group,
            "music_selection": "random",
            "cover_template_id": cover_template.id,
            "video_settings": {
                "resolution": "1080x1920",
                "fps": 30,
                "format": "mp4"
            }
        }
        print("   - 任务配置已创建。")

        # --- 3. 执行任务 ---
        print("\n3. 正在执行视频生成流程... (这可能需要几分钟时间)")
        
        await video_gen_service._execute_task(task.id, job_config)

        # --- 4. 显示结果 ---
        # Re-fetch the task in the current session to get the latest data
        db.refresh(task)
        
        print("\n--- 演示完成 ---")
        if task.status == TaskStatus.COMPLETED:
            print(f"视频生成成功！")
            final_path = Path(task.final_video_path).resolve()
            print(f"   视频文件位于: {final_path}")
        else:
            print(f"视频生成失败。")
            print(f"   任务状态: {task.status}")
            print(f"   错误信息: {task.error_message}")

    except Exception:
        print("\n--- 演示过程中发生严重错误 ---")
        traceback.print_exc()

    finally:
        db.close()


if __name__ == "__main__":
    if not (backend_path / '.env').exists():
        print("错误: 在 'backend' 目录下没有找到 .env 文件。")
        print("请从 .env.example 创建一个 .env 文件，并填入您的API密钥。")
    else:
        asyncio.run(run_demo())
