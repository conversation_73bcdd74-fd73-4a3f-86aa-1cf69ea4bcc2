#!/usr/bin/env python3
"""
测试封面可见性修复 - 确保封面正确显示且有fade效果
"""

import sys
import os
import logging
from pathlib import Path
import ffmpeg
import tempfile
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.video_generation_helpers import VideoCompositionService

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_cover_visibility():
    """测试封面可见性修复"""
    
    # 测试文件路径
    video_path = "uploads/videos/RedditTour_07030014_视频1.mp4"
    cover_path = "uploads/covers/1_02c58438-97a3-435f-8ed0-5b669bc167ed_cover.png"
    
    # 检查文件是否存在
    if not Path(video_path).exists():
        logger.error(f"测试视频文件不存在: {video_path}")
        return False
        
    if not Path(cover_path).exists():
        logger.error(f"测试封面文件不存在: {cover_path}")
        return False
    
    logger.info("🔄 开始测试封面可见性修复...")
    logger.info("关键修复：loop=总帧数, size=1, 确保封面完整显示")
    
    # 测试配置 - 使用较长时间确保能看到封面
    test_configs = [
        {
            'name': '封面可见性-淡入测试',
            'animation': 'fade_in',
            'duration': 8.0,  # 8秒显示
            'animation_duration': 3.0,  # 3秒淡入动画
            'output': 'visibility_fade_in.mp4'
        },
        {
            'name': '封面可见性-淡出测试', 
            'animation': 'fade_out',
            'duration': 8.0,  # 8秒显示
            'animation_duration': 3.0,  # 3秒淡出动画
            'output': 'visibility_fade_out.mp4'
        },
        {
            'name': '封面可见性-淡入淡出测试',
            'animation': 'fade_in_out',
            'duration': 10.0,  # 10秒显示
            'animation_duration': 2.5,  # 各2.5秒动画
            'output': 'visibility_fade_in_out.mp4'
        },
        {
            'name': '封面可见性-无动画测试',
            'animation': 'none',
            'duration': 6.0,  # 6秒显示
            'animation_duration': 0.0,  # 无动画
            'output': 'visibility_no_animation.mp4'
        }
    ]
    
    try:
        # 获取视频信息
        probe = ffmpeg.probe(video_path)
        video_info = next(s for s in probe['streams'] if s['codec_type'] == 'video')
        width = int(video_info['width'])
        height = int(video_info['height'])
        
        logger.info(f"视频信息: {width}x{height}")
        
        # 计算封面参数 - 使用较大的封面确保可见
        cover_width = int(width * 0.7)  # 70%宽度，更明显
        corner_radius = int(cover_width * 0.06)
        
        logger.info(f"封面配置: 宽度={cover_width}px, 圆角={corner_radius}px")
        
        success_count = 0
        
        for i, config in enumerate(test_configs):
            logger.info(f"\n--- 测试 {i+1}: {config['name']} ---")
            logger.info(f"动画类型: {config['animation']}")
            logger.info(f"显示时长: {config['duration']}s")
            logger.info(f"动画时长: {config['animation_duration']}s")
            logger.info(f"输出文件: {config['output']}")
            
            try:
                # 创建临时目录
                with tempfile.TemporaryDirectory() as temp_dir:
                    temp_path = Path(temp_dir)
                    
                    # 创建圆角封面
                    rounded_cover_path = temp_path / f"visibility_rounded_cover_{i}.png"
                    
                    success = VideoCompositionService._create_rounded_cover_image(
                        cover_path, cover_width, corner_radius, str(rounded_cover_path)
                    )
                    
                    if not success or not rounded_cover_path.exists():
                        logger.error(f"❌ 圆角封面创建失败: {config['name']}")
                        continue
                    
                    # 创建视频流
                    video_stream = ffmpeg.input(video_path)
                    cover_overlay = ffmpeg.input(str(rounded_cover_path))
                    
                    # 封面设置
                    cover_settings = {
                        'position': 'center',
                        'animation': config['animation'],
                        'animation_duration': config['animation_duration']
                    }
                    
                    logger.info("开始应用封面叠加效果...")
                    start_time = time.time()
                    
                    # 应用封面叠加效果（使用修复后的代码）
                    final_video = VideoCompositionService._apply_cover_overlay(
                        video_stream, cover_overlay, config['duration'], cover_settings
                    )
                    
                    # 输出视频
                    total_duration = config['duration'] + 2  # 多2秒缓冲
                    out = ffmpeg.output(
                        final_video,
                        config['output'],
                        vcodec='libx264',
                        preset='fast',  # 使用fast预设，平衡速度和质量
                        pix_fmt='yuv420p',
                        t=total_duration
                    ).overwrite_output()
                    
                    logger.info(f"开始生成{total_duration}秒的测试视频...")
                    
                    # 执行，设置超时
                    import subprocess
                    
                    cmd = ffmpeg.compile(out)
                    logger.info(f"FFmpeg命令长度: {len(' '.join(cmd))} 字符")
                    
                    # 使用subprocess执行，设置超时
                    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                    
                    try:
                        # 等待最多120秒
                        stdout, stderr = process.communicate(timeout=120)
                        
                        if process.returncode == 0:
                            end_time = time.time()
                            processing_time = end_time - start_time
                            
                            # 检查结果
                            if Path(config['output']).exists():
                                file_size = Path(config['output']).stat().st_size
                                logger.info(f"✅ {config['name']} 测试成功!")
                                logger.info(f"   文件大小: {file_size} bytes")
                                logger.info(f"   处理时间: {processing_time:.2f}秒")
                                
                                # 详细说明预期效果
                                if config['animation'] == 'fade_in':
                                    logger.info(f"   预期效果: 0-{config['animation_duration']}秒封面慢慢淡入，然后保持显示")
                                elif config['animation'] == 'fade_out':
                                    fade_start = config['duration'] - config['animation_duration']
                                    logger.info(f"   预期效果: 0-{fade_start}秒封面正常显示，{fade_start}-{config['duration']}秒慢慢淡出")
                                elif config['animation'] == 'fade_in_out':
                                    fade_out_start = config['duration'] - config['animation_duration']
                                    logger.info(f"   预期效果: 0-{config['animation_duration']}秒淡入，{fade_out_start}-{config['duration']}秒淡出")
                                elif config['animation'] == 'none':
                                    logger.info(f"   预期效果: 0-{config['duration']}秒封面正常显示，无动画")
                                
                                success_count += 1
                            else:
                                logger.error(f"❌ {config['name']} 测试失败: 输出文件不存在")
                        else:
                            logger.error(f"❌ {config['name']} FFmpeg执行失败，返回码: {process.returncode}")
                            logger.error(f"stderr: {stderr.decode('utf-8', errors='ignore')[:500]}...")
                            
                    except subprocess.TimeoutExpired:
                        logger.error(f"❌ {config['name']} 测试超时（120秒）")
                        process.kill()
                        process.communicate()
                        
            except Exception as e:
                logger.error(f"❌ {config['name']} 测试过程中发生错误: {e}")
                import traceback
                logger.error(traceback.format_exc())
        
        logger.info(f"\n=== 封面可见性测试完成 ===")
        logger.info(f"成功: {success_count}/{len(test_configs)} 个测试")
        
        if success_count > 0:
            logger.info("\n📋 封面可见性测试文件:")
            logger.info("- visibility_fade_in.mp4 (封面可见性-淡入)")
            logger.info("- visibility_fade_out.mp4 (封面可见性-淡出)")
            logger.info("- visibility_fade_in_out.mp4 (封面可见性-淡入淡出)")
            logger.info("- visibility_no_animation.mp4 (封面可见性-无动画)")
            
            logger.info("\n🔍 关键修复点:")
            logger.info("1. Loop参数修复：loop=总帧数, size=1")
            logger.info("2. 确保封面覆盖整个duration时间")
            logger.info("3. 双重保险：loop滤镜 + enable时间限制")
            logger.info("4. 避免无限循环的同时保证可见性")
            
            logger.info("\n🎬 验证方法:")
            logger.info("播放visibility_*.mp4文件，应该能看到:")
            logger.info("- 封面在整个duration期间都可见")
            logger.info("- 有相应的fade动画效果")
            logger.info("- 处理速度合理（不会卡死几小时）")
            
            logger.info("\n如果封面可见且有fade效果，说明问题完全解决！")
            
            return True
        else:
            logger.error("❌ 所有测试都失败了")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    logger.info("🚀 开始封面可见性测试")
    logger.info("本次修复：确保loop参数正确，封面完整显示且有fade效果")
    
    success = test_cover_visibility()
    
    if success:
        logger.info("\n🎉 封面可见性测试完成!")
        logger.info("如果封面可见且有fade效果，说明所有问题都解决了！")
        logger.info("现在可以重新生成你的视频，应该既快速又有完美的fade效果。")
    else:
        logger.error("\n❌ 封面可见性测试失败")
        logger.error("需要进一步调试loop参数")
        sys.exit(1)
