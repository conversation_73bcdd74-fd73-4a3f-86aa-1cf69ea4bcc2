#!/usr/bin/env python3
"""
调试FFmpeg命令 - 看看到底生成了什么命令
"""

import sys
import os
import logging
from pathlib import Path
import ffmpeg
import tempfile

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.video_generation_helpers import VideoCompositionService

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def debug_ffmpeg_commands():
    """调试FFmpeg命令生成"""
    
    video_path = "uploads/videos/RedditTour_07030014_视频1.mp4"
    cover_path = "uploads/covers/1_02c58438-97a3-435f-8ed0-5b669bc167ed_cover.png"
    
    if not Path(video_path).exists() or not Path(cover_path).exists():
        logger.error("测试文件不存在")
        return False
    
    logger.info("🔍 开始调试FFmpeg命令生成...")
    
    try:
        # 获取视频信息
        probe = ffmpeg.probe(video_path)
        video_info = next(s for s in probe['streams'] if s['codec_type'] == 'video')
        width = int(video_info['width'])
        height = int(video_info['height'])
        
        # 计算封面参数
        cover_width = int(width * 0.8)
        corner_radius = int(cover_width * 0.06)
        
        logger.info(f"视频尺寸: {width}x{height}")
        logger.info(f"封面宽度: {cover_width}px")
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            rounded_cover_path = temp_path / "debug_rounded_cover.png"
            
            # 创建圆角封面
            success = VideoCompositionService._create_rounded_cover_image(
                cover_path, cover_width, corner_radius, str(rounded_cover_path)
            )
            
            if not success:
                logger.error("圆角封面创建失败")
                return False
            
            logger.info("✅ 圆角封面创建成功")
            
            # 测试不同的fade命令
            test_cases = [
                {
                    'name': '测试1: 基础淡入',
                    'fade_params': {'t': 'in', 'st': 0, 'd': 2},
                    'output': 'debug_fade_test1.mp4'
                },
                {
                    'name': '测试2: 基础淡出', 
                    'fade_params': {'t': 'out', 'st': 3, 'd': 2},
                    'output': 'debug_fade_test2.mp4'
                },
                {
                    'name': '测试3: 使用帧数淡入',
                    'fade_params': {'type': 'in', 'start_frame': 0, 'nb_frames': 60},  # 60帧 = 2秒 @ 30fps
                    'output': 'debug_fade_test3.mp4'
                }
            ]
            
            for i, test_case in enumerate(test_cases):
                logger.info(f"\n--- {test_case['name']} ---")
                
                try:
                    # 创建视频流
                    video_input = ffmpeg.input(video_path)
                    cover_input = ffmpeg.input(str(rounded_cover_path))
                    
                    # 应用fade效果
                    logger.info(f"Fade参数: {test_case['fade_params']}")
                    cover_with_fade = cover_input.filter('fade', **test_case['fade_params'])
                    
                    # 叠加到视频
                    output = video_input.overlay(
                        cover_with_fade,
                        x='(main_w-overlay_w)/2',
                        y='(main_h-overlay_h)/2',
                        enable='between(t,0,6)'
                    )
                    
                    # 生成输出
                    out = ffmpeg.output(
                        output,
                        test_case['output'],
                        vcodec='libx264',
                        preset='fast',
                        pix_fmt='yuv420p',
                        t=7
                    ).overwrite_output()
                    
                    # 显示完整的FFmpeg命令
                    cmd = ffmpeg.compile(out)
                    logger.info(f"完整FFmpeg命令:")
                    logger.info(f"{' '.join(cmd)}")
                    
                    # 执行命令
                    logger.info("执行FFmpeg命令...")
                    ffmpeg.run(out, quiet=False, capture_stdout=True, capture_stderr=True)
                    
                    # 检查结果
                    if Path(test_case['output']).exists():
                        file_size = Path(test_case['output']).stat().st_size
                        logger.info(f"✅ {test_case['output']} 生成成功, 大小: {file_size} bytes")
                    else:
                        logger.error(f"❌ {test_case['output']} 生成失败")
                        
                except Exception as e:
                    logger.error(f"❌ {test_case['name']} 执行失败: {e}")
            
            # 测试最简单的fade命令
            logger.info(f"\n--- 测试4: 最简单的fade命令 ---")
            try:
                # 直接用命令行方式测试
                simple_cmd = [
                    'ffmpeg', '-y',
                    '-i', video_path,
                    '-i', str(rounded_cover_path),
                    '-filter_complex', 
                    '[1:v]fade=t=in:st=0:d=2[faded];[0:v][faded]overlay=(main_w-overlay_w)/2:(main_h-overlay_h)/2:enable=between(t,0,6)',
                    '-t', '7',
                    '-c:v', 'libx264',
                    '-preset', 'fast',
                    '-pix_fmt', 'yuv420p',
                    'debug_fade_simple.mp4'
                ]
                
                logger.info("最简单的FFmpeg命令:")
                logger.info(' '.join(simple_cmd))
                
                import subprocess
                result = subprocess.run(simple_cmd, capture_output=True, text=True)
                
                if result.returncode == 0:
                    if Path('debug_fade_simple.mp4').exists():
                        file_size = Path('debug_fade_simple.mp4').stat().st_size
                        logger.info(f"✅ debug_fade_simple.mp4 生成成功, 大小: {file_size} bytes")
                    else:
                        logger.error("❌ debug_fade_simple.mp4 文件不存在")
                else:
                    logger.error(f"❌ FFmpeg执行失败:")
                    logger.error(f"stdout: {result.stdout}")
                    logger.error(f"stderr: {result.stderr}")
                    
            except Exception as e:
                logger.error(f"❌ 简单命令测试失败: {e}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 调试过程失败: {e}")
        return False

if __name__ == "__main__":
    logger.info("🚀 开始FFmpeg命令调试")
    debug_ffmpeg_commands()
    logger.info("🏁 调试完成")
