#!/usr/bin/env python3
"""
测试Excel解析功能
"""

import pandas as pd
import io

def test_excel_parsing():
    """测试Excel解析逻辑"""
    
    # 读取测试Excel文件
    df = pd.read_excel('test_excel_with_titles.xlsx')
    
    print("原始DataFrame:")
    print(df)
    print()
    
    # 模拟后端解析逻辑
    if df.empty:
        print("Excel文件为空")
        return
    
    # 获取第一列数据，过滤空值
    first_column = df.iloc[:, 0].dropna()
    stories = [str(story).strip() for story in first_column if str(story).strip()]
    
    if not stories:
        print("Excel文件中没有找到有效的文案内容")
        return
    
    # 获取第二列数据（标题），如果存在的话
    titles = []
    if df.shape[1] >= 2:  # 检查是否有第二列
        second_column = df.iloc[:, 1]
        # 为每个文案对应一个标题，如果标题为空则使用空字符串
        for i in range(len(stories)):
            if i < len(second_column) and pd.notna(second_column.iloc[i]):
                title = str(second_column.iloc[i]).strip()
                titles.append(title)
            else:
                titles.append("")  # 空标题
    else:
        # 如果没有第二列，所有标题都为空
        titles = [""] * len(stories)
    
    print(f"解析结果:")
    print(f"文案数量: {len(stories)}")
    print(f"标题数量: {len(titles)}")
    print()
    
    for i, (story, title) in enumerate(zip(stories, titles)):
        print(f"#{i+1}:")
        print(f"  文案: {story}")
        print(f"  标题: {title if title else '(无标题)'}")
        print()
    
    # 测试消息生成
    title_count = len([t for t in titles if t])
    message = f"成功解析 {len(stories)} 条文案"
    if title_count > 0:
        message += f"，其中 {title_count} 条有标题"
    
    print(f"消息: {message}")

if __name__ == "__main__":
    test_excel_parsing()
