#!/usr/bin/env python3
"""
测试新的任务状态
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

try:
    from src.models.video_generation import TaskStatus
    print("✅ 成功导入TaskStatus")
    
    # 测试新状态
    print(f"AUDIO_PENDING: {TaskStatus.AUDIO_PENDING}")
    print(f"AUDIO_COMPLETED: {TaskStatus.AUDIO_COMPLETED}")
    print(f"VIDEO_PENDING: {TaskStatus.VIDEO_PENDING}")
    
    # 测试所有状态
    print("\n所有状态:")
    for status in TaskStatus:
        print(f"  {status.name}: {status.value}")
    
    print("\n✅ 新状态测试成功！")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
