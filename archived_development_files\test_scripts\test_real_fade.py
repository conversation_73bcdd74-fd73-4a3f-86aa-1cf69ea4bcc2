#!/usr/bin/env python3
"""
真正的fade测试 - 不用任何封装，直接测试FFmpeg fade效果
"""

import subprocess
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_real_fade():
    """直接测试FFmpeg fade效果"""
    
    video_path = "uploads/videos/RedditTour_07030014_视频1.mp4"
    cover_path = "uploads/covers/1_02c58438-97a3-435f-8ed0-5b669bc167ed_cover.png"
    
    if not Path(video_path).exists() or not Path(cover_path).exists():
        logger.error("测试文件不存在")
        return False
    
    logger.info("🔄 开始真正的fade测试...")
    
    # 测试1: 最基础的fade in
    logger.info("\n--- 测试1: 基础fade in ---")
    cmd1 = [
        'ffmpeg', '-y',
        '-i', video_path,
        '-i', cover_path,
        '-filter_complex', 
        '[1:v]scale=864:-1,fade=in:0:60[faded];[0:v][faded]overlay=(main_w-overlay_w)/2:(main_h-overlay_h)/2',
        '-t', '5',
        '-c:v', 'libx264',
        '-preset', 'ultrafast',
        'real_fade_in.mp4'
    ]
    
    logger.info(f"命令: {' '.join(cmd1)}")
    result1 = subprocess.run(cmd1, capture_output=True, text=True)
    
    if result1.returncode == 0:
        logger.info("✅ real_fade_in.mp4 生成成功")
    else:
        logger.error(f"❌ 失败: {result1.stderr}")
    
    # 测试2: fade out
    logger.info("\n--- 测试2: 基础fade out ---")
    cmd2 = [
        'ffmpeg', '-y',
        '-i', video_path,
        '-i', cover_path,
        '-filter_complex', 
        '[1:v]scale=864:-1,fade=out:90:60[faded];[0:v][faded]overlay=(main_w-overlay_w)/2:(main_h-overlay_h)/2',
        '-t', '5',
        '-c:v', 'libx264',
        '-preset', 'ultrafast',
        'real_fade_out.mp4'
    ]
    
    logger.info(f"命令: {' '.join(cmd2)}")
    result2 = subprocess.run(cmd2, capture_output=True, text=True)
    
    if result2.returncode == 0:
        logger.info("✅ real_fade_out.mp4 生成成功")
    else:
        logger.error(f"❌ 失败: {result2.stderr}")
    
    # 测试3: 使用时间的fade
    logger.info("\n--- 测试3: 使用时间的fade ---")
    cmd3 = [
        'ffmpeg', '-y',
        '-i', video_path,
        '-i', cover_path,
        '-filter_complex', 
        '[1:v]scale=864:-1,fade=t=in:st=0:d=2[faded];[0:v][faded]overlay=(main_w-overlay_w)/2:(main_h-overlay_h)/2',
        '-t', '5',
        '-c:v', 'libx264',
        '-preset', 'ultrafast',
        'real_fade_time.mp4'
    ]
    
    logger.info(f"命令: {' '.join(cmd3)}")
    result3 = subprocess.run(cmd3, capture_output=True, text=True)
    
    if result3.returncode == 0:
        logger.info("✅ real_fade_time.mp4 生成成功")
    else:
        logger.error(f"❌ 失败: {result3.stderr}")
    
    # 测试4: 不用overlay的enable，直接fade
    logger.info("\n--- 测试4: 纯fade测试 ---")
    cmd4 = [
        'ffmpeg', '-y',
        '-i', cover_path,
        '-filter_complex', 
        '[0:v]scale=864:-1,fade=in:0:60,fade=out:90:60',
        '-t', '5',
        '-c:v', 'libx264',
        '-preset', 'ultrafast',
        'pure_fade_test.mp4'
    ]
    
    logger.info(f"命令: {' '.join(cmd4)}")
    result4 = subprocess.run(cmd4, capture_output=True, text=True)
    
    if result4.returncode == 0:
        logger.info("✅ pure_fade_test.mp4 生成成功")
    else:
        logger.error(f"❌ 失败: {result4.stderr}")
    
    # 测试5: 分步骤 - 先生成fade的封面，再overlay
    logger.info("\n--- 测试5: 分步骤测试 ---")
    
    # 步骤1: 生成fade封面
    cmd5a = [
        'ffmpeg', '-y',
        '-i', cover_path,
        '-filter_complex', 
        '[0:v]scale=864:-1,fade=in:0:60,fade=out:90:60,loop=-1:1:0',
        '-t', '5',
        '-c:v', 'libx264',
        '-preset', 'ultrafast',
        'faded_cover.mp4'
    ]
    
    logger.info(f"步骤1命令: {' '.join(cmd5a)}")
    result5a = subprocess.run(cmd5a, capture_output=True, text=True)
    
    if result5a.returncode == 0:
        logger.info("✅ faded_cover.mp4 生成成功")
        
        # 步骤2: overlay到视频
        cmd5b = [
            'ffmpeg', '-y',
            '-i', video_path,
            '-i', 'faded_cover.mp4',
            '-filter_complex', 
            '[0:v][1:v]overlay=(main_w-overlay_w)/2:(main_h-overlay_h)/2',
            '-t', '5',
            '-c:v', 'libx264',
            '-preset', 'ultrafast',
            'step_by_step_fade.mp4'
        ]
        
        logger.info(f"步骤2命令: {' '.join(cmd5b)}")
        result5b = subprocess.run(cmd5b, capture_output=True, text=True)
        
        if result5b.returncode == 0:
            logger.info("✅ step_by_step_fade.mp4 生成成功")
        else:
            logger.error(f"❌ 步骤2失败: {result5b.stderr}")
    else:
        logger.error(f"❌ 步骤1失败: {result5a.stderr}")
    
    # 检查生成的文件
    logger.info("\n=== 生成的文件检查 ===")
    test_files = [
        'real_fade_in.mp4',
        'real_fade_out.mp4', 
        'real_fade_time.mp4',
        'pure_fade_test.mp4',
        'faded_cover.mp4',
        'step_by_step_fade.mp4'
    ]
    
    for filename in test_files:
        if Path(filename).exists():
            file_size = Path(filename).stat().st_size
            logger.info(f"✅ {filename}: {file_size} bytes")
        else:
            logger.info(f"❌ {filename}: 不存在")
    
    logger.info("\n🎬 测试完成！请播放这些文件检查fade效果:")
    logger.info("- real_fade_in.mp4: 基础淡入")
    logger.info("- real_fade_out.mp4: 基础淡出")
    logger.info("- real_fade_time.mp4: 时间参数淡入")
    logger.info("- pure_fade_test.mp4: 纯封面fade测试")
    logger.info("- step_by_step_fade.mp4: 分步骤fade测试")
    
    return True

if __name__ == "__main__":
    test_real_fade()
