#!/usr/bin/env python3
"""
测试修复后的代码 - 使用成功验证的loop+帧数方法
"""

import sys
import os
import logging
from pathlib import Path
import ffmpeg
import tempfile

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.video_generation_helpers import VideoCompositionService

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_fixed_code():
    """测试修复后的代码"""
    
    # 测试文件路径
    video_path = "uploads/videos/RedditTour_07030014_视频1.mp4"
    cover_path = "uploads/covers/1_02c58438-97a3-435f-8ed0-5b669bc167ed_cover.png"
    
    # 检查文件是否存在
    if not Path(video_path).exists():
        logger.error(f"测试视频文件不存在: {video_path}")
        return False
        
    if not Path(cover_path).exists():
        logger.error(f"测试封面文件不存在: {cover_path}")
        return False
    
    logger.info("🔄 开始测试修复后的代码...")
    logger.info("关键修复：使用loop滤镜+帧数参数，基于成功验证的方法")
    
    # 测试配置 - 使用更长的时间确保能看清楚
    test_configs = [
        {
            'name': '修复代码-超明显淡入',
            'animation': 'fade_in',
            'duration': 10.0,  # 10秒显示
            'animation_duration': 4.0,  # 4秒淡入动画
            'output': 'fixed_code_fade_in.mp4'
        },
        {
            'name': '修复代码-超明显淡出', 
            'animation': 'fade_out',
            'duration': 10.0,  # 10秒显示
            'animation_duration': 4.0,  # 4秒淡出动画
            'output': 'fixed_code_fade_out.mp4'
        },
        {
            'name': '修复代码-超明显淡入淡出',
            'animation': 'fade_in_out',
            'duration': 12.0,  # 12秒显示
            'animation_duration': 3.0,  # 各3秒动画
            'output': 'fixed_code_fade_in_out.mp4'
        }
    ]
    
    try:
        # 获取视频信息
        probe = ffmpeg.probe(video_path)
        video_info = next(s for s in probe['streams'] if s['codec_type'] == 'video')
        width = int(video_info['width'])
        height = int(video_info['height'])
        
        logger.info(f"视频信息: {width}x{height}")
        
        # 计算封面参数
        cover_width = int(width * 0.8)
        corner_radius = int(cover_width * 0.06)
        
        logger.info(f"封面配置: 宽度={cover_width}px, 圆角={corner_radius}px")
        
        success_count = 0
        
        for i, config in enumerate(test_configs):
            logger.info(f"\n--- 测试 {i+1}: {config['name']} ---")
            logger.info(f"动画类型: {config['animation']}")
            logger.info(f"显示时长: {config['duration']}s")
            logger.info(f"动画时长: {config['animation_duration']}s")
            logger.info(f"输出文件: {config['output']}")
            
            try:
                # 创建临时目录
                with tempfile.TemporaryDirectory() as temp_dir:
                    temp_path = Path(temp_dir)
                    
                    # 创建圆角封面
                    rounded_cover_path = temp_path / f"fixed_code_rounded_cover_{i}.png"
                    
                    success = VideoCompositionService._create_rounded_cover_image(
                        cover_path, cover_width, corner_radius, str(rounded_cover_path)
                    )
                    
                    if not success or not rounded_cover_path.exists():
                        logger.error(f"❌ 圆角封面创建失败: {config['name']}")
                        continue
                    
                    # 创建视频流
                    video_stream = ffmpeg.input(video_path)
                    cover_overlay = ffmpeg.input(str(rounded_cover_path))
                    
                    # 封面设置
                    cover_settings = {
                        'position': 'center',
                        'animation': config['animation'],
                        'animation_duration': config['animation_duration']
                    }
                    
                    # 应用封面叠加效果（使用修复后的代码）
                    final_video = VideoCompositionService._apply_cover_overlay(
                        video_stream, cover_overlay, config['duration'], cover_settings
                    )
                    
                    # 输出视频
                    total_duration = config['duration'] + 2  # 多2秒缓冲
                    out = ffmpeg.output(
                        final_video,
                        config['output'],
                        vcodec='libx264',
                        preset='fast',
                        pix_fmt='yuv420p',
                        t=total_duration
                    ).overwrite_output()
                    
                    logger.info(f"开始生成{total_duration}秒的测试视频...")
                    
                    # 显示FFmpeg命令（用于调试）
                    cmd = ffmpeg.compile(out)
                    logger.info(f"FFmpeg命令长度: {len(' '.join(cmd))} 字符")
                    
                    # 执行
                    ffmpeg.run(out, quiet=True)
                    
                    # 检查结果
                    if Path(config['output']).exists():
                        file_size = Path(config['output']).stat().st_size
                        logger.info(f"✅ {config['name']} 测试成功! 文件大小: {file_size} bytes")
                        
                        # 详细说明预期效果
                        if config['animation'] == 'fade_in':
                            logger.info(f"   预期效果: 0-{config['animation_duration']}秒封面慢慢淡入")
                        elif config['animation'] == 'fade_out':
                            fade_start = config['duration'] - config['animation_duration']
                            logger.info(f"   预期效果: {fade_start}-{config['duration']}秒封面慢慢淡出")
                        elif config['animation'] == 'fade_in_out':
                            fade_out_start = config['duration'] - config['animation_duration']
                            logger.info(f"   预期效果: 0-{config['animation_duration']}秒淡入，{fade_out_start}-{config['duration']}秒淡出")
                        
                        success_count += 1
                    else:
                        logger.error(f"❌ {config['name']} 测试失败: 输出文件不存在")
                        
            except Exception as e:
                logger.error(f"❌ {config['name']} 测试过程中发生错误: {e}")
                import traceback
                logger.error(traceback.format_exc())
        
        logger.info(f"\n=== 修复代码测试完成 ===")
        logger.info(f"成功: {success_count}/{len(test_configs)} 个测试")
        
        if success_count > 0:
            logger.info("\n📋 修复代码测试文件:")
            logger.info("- fixed_code_fade_in.mp4 (修复代码-超明显淡入)")
            logger.info("- fixed_code_fade_out.mp4 (修复代码-超明显淡出)")
            logger.info("- fixed_code_fade_in_out.mp4 (修复代码-超明显淡入淡出)")
            
            logger.info("\n🔍 关键修复点:")
            logger.info("1. 添加loop滤镜：让静态图片变成视频流")
            logger.info("2. 使用帧数参数：fade=type:start_frame:nb_frames")
            logger.info("3. 移除enable参数：避免与fade效果冲突")
            logger.info("4. 基于成功验证的方法实现")
            
            logger.info("\n🎬 验证方法:")
            logger.info("播放fixed_code_*.mp4文件，应该能看到:")
            logger.info("- 明显的淡入淡出动画效果")
            logger.info("- 平滑的透明度变化")
            logger.info("- 没有突然出现/消失的跳跃")
            
            logger.info("\n如果这些文件有明显的fade效果，")
            logger.info("说明代码修复成功！可以重新生成你的视频了。")
            
            return True
        else:
            logger.error("❌ 所有测试都失败了")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    logger.info("🚀 开始修复代码测试")
    logger.info("本次修复基于成功验证的方法：loop滤镜+帧数参数")
    
    success = test_fixed_code()
    
    if success:
        logger.info("\n🎉 修复代码测试完成!")
        logger.info("如果这些文件有fade效果，说明问题已解决！")
        logger.info("现在可以重新生成你的视频，fade效果应该正常工作。")
    else:
        logger.error("\n❌ 修复代码测试失败")
        logger.error("需要进一步调试问题")
        sys.exit(1)
