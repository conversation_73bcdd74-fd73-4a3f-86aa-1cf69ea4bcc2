'use client'

import React, { useState, useEffect } from 'react'
import {
  UserGroupIcon,
  ArrowPathIcon,
  PencilIcon,
  XMarkIcon
} from '@heroicons/react/24/outline'

interface Account {
  id: string
  name: string
}

interface StoryAssignmentTableProps {
  stories: string[]
  titles: string[]
  genders: string[]
  accounts: Account[]
  selectedAccounts: string[]
  onAssignmentChange: (assignments: { [key: number]: string }) => void
  className?: string
}

export default function StoryAssignmentTable({
  stories,
  titles,
  genders,
  accounts,
  selectedAccounts,
  onAssignmentChange,
  className = ''
}: StoryAssignmentTableProps) {
  const [assignments, setAssignments] = useState<{ [key: number]: string }>({})
  const [editingIndex, setEditingIndex] = useState<number | null>(null)

  // 获取选中的账号列表
  const availableAccounts = accounts.filter(account =>
    selectedAccounts.includes(String(account.id))
  )

  // 初始化分配和自动分配
  useEffect(() => {
    if (stories.length > 0 && availableAccounts.length > 0) {
      autoAssignStories()
    }
  }, [stories.length, selectedAccounts.length])

  // 自动按序分配故事给账号
  const autoAssignStories = () => {
    if (availableAccounts.length === 0) return

    const newAssignments: { [key: number]: string } = {}
    stories.forEach((_, index) => {
      const accountIndex = index % availableAccounts.length
      newAssignments[index] = String(availableAccounts[accountIndex].id)
    })

    setAssignments(newAssignments)
    onAssignmentChange(newAssignments)
  }

  // 手动修改单个故事的账号分配
  const handleAssignmentChange = (storyIndex: number, accountId: string) => {
    const newAssignments = { ...assignments, [storyIndex]: accountId }
    setAssignments(newAssignments)
    onAssignmentChange(newAssignments)
    setEditingIndex(null)
  }

  // 获取账号名称
  const getAccountName = (accountId: string) => {
    const account = accounts.find(acc => String(acc.id) === accountId)
    return account ? account.name : '未知账号'
  }

  // 获取性别显示文本和颜色
  const getGenderDisplay = (gender: string) => {
    if (gender === 'male') {
      return { text: '男性', color: 'text-blue-600' }
    } else if (gender === 'female') {
      return { text: '女性', color: 'text-pink-600' }
    }
    return { text: '未指定', color: 'text-gray-500' }
  }

  if (stories.length === 0) {
    return (
      <div className={`bg-white rounded-lg shadow-sm p-6 ${className}`}>
        <div className="text-center text-gray-500">
          <UserGroupIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <p>请先上传Excel文件解析故事文案</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <UserGroupIcon className="h-5 w-5 text-blue-500 mr-2" />
          <h2 className="text-lg font-semibold text-gray-900">故事编排</h2>
        </div>
        <button
          onClick={autoAssignStories}
          disabled={availableAccounts.length === 0 || stories.length === 0}
          className="flex items-center px-3 py-2 text-sm bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
        >
          <ArrowPathIcon className="h-4 w-4 mr-1" />
          一键自动编排
        </button>
      </div>

      {availableAccounts.length === 0 && (
        <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-sm text-yellow-800">
            请先选择至少一个账号才能进行故事编排
          </p>
        </div>
      )}

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                序号
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                故事文案
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                标题
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                性别
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                分配账号
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {stories.map((story, index) => {
              const title = titles[index] || ''
              const gender = genders[index] || 'female'
              const assignedAccountId = assignments[index]
              const genderDisplay = getGenderDisplay(gender)
              const isEditing = editingIndex === index

              return (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    #{index + 1}
                  </td>
                  <td className="px-4 py-4 text-sm text-gray-900">
                    <div className="max-w-xs truncate" title={story}>
                      {story}
                    </div>
                  </td>
                  <td className="px-4 py-4 text-sm text-gray-900">
                    <div className="max-w-xs truncate" title={title}>
                      {title || <span className="text-gray-400">无标题</span>}
                    </div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm">
                    <span className={`font-medium ${genderDisplay.color}`}>
                      {genderDisplay.text}
                    </span>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    {isEditing ? (
                      <select
                        value={assignedAccountId || ''}
                        onChange={(e) => handleAssignmentChange(index, e.target.value)}
                        className="text-sm border border-gray-300 rounded px-2 py-1 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        autoFocus
                      >
                        <option value="">选择账号</option>
                        {availableAccounts.map(account => (
                          <option key={account.id} value={account.id}>
                            {account.name}
                          </option>
                        ))}
                      </select>
                    ) : (
                      <span className="font-medium text-blue-600">
                        {assignedAccountId ? getAccountName(assignedAccountId) : '未分配'}
                      </span>
                    )}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                    {isEditing ? (
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => setEditingIndex(null)}
                          className="text-gray-400 hover:text-gray-600"
                        >
                          <XMarkIcon className="h-4 w-4" />
                        </button>
                      </div>
                    ) : (
                      <button
                        onClick={() => setEditingIndex(index)}
                        disabled={availableAccounts.length === 0}
                        className="text-blue-500 hover:text-blue-700 disabled:text-gray-300 disabled:cursor-not-allowed"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                    )}
                  </td>
                </tr>
              )
            })}
          </tbody>
        </table>
      </div>

      {stories.length > 0 && availableAccounts.length > 0 && (
        <div className="mt-4 p-3 bg-blue-50 rounded-lg">
          <p className="text-sm text-blue-800">
            共 {stories.length} 个故事，将分配给 {availableAccounts.length} 个账号生成视频
          </p>
        </div>
      )}
    </div>
  )
}
